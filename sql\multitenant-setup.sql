-- =====================================================
-- SETUP MULTI-TENANT PARA PRIMEAI
-- Domínio: vascofa.shop
-- =====================================================

-- 1. CRIAR TABELA DE TENANTS
-- =====================================================
CREATE TABLE IF NOT EXISTS tenants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  subdomain VARCHAR(50) UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  logo_url TEXT,
  primary_color VARCHAR(7) DEFAULT '#000000',
  secondary_color VARCHAR(7) DEFAULT '#ffffff',
  custom_css TEXT,
  settings JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_tenants_subdomain ON tenants(subdomain);
CREATE INDEX IF NOT EXISTS idx_tenants_active ON tenants(is_active);

-- 2. ADICIONAR TENANT_ID ÀS TABELAS EXISTENTES
-- =====================================================

-- Tabela de custos de interação
ALTER TABLE agent_interaction_costs 
ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);

-- Tabelas de dados financeiros
ALTER TABLE cotacoes_historicas_b3 
ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);

ALTER TABLE curvas_b3 
ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);

ALTER TABLE cri_cra_precos 
ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);

ALTER TABLE debentures_precos 
ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);

ALTER TABLE titulos_publicos_precos 
ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);

ALTER TABLE letras_precos 
ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);

-- 3. CRIAR ÍNDICES COMPOSTOS PARA PERFORMANCE
-- =====================================================
CREATE INDEX IF NOT EXISTS idx_costs_tenant_user 
ON agent_interaction_costs(tenant_id, user_id);

CREATE INDEX IF NOT EXISTS idx_cotacoes_tenant_data 
ON cotacoes_historicas_b3(tenant_id, data_pregao);

CREATE INDEX IF NOT EXISTS idx_curvas_tenant_data 
ON curvas_b3(tenant_id, data_referencia);

-- 4. FUNÇÃO PARA DEFINIR TENANT ATUAL
-- =====================================================
CREATE OR REPLACE FUNCTION set_current_tenant(tenant_uuid UUID)
RETURNS void AS $$
BEGIN
  PERFORM set_config('app.current_tenant_id', tenant_uuid::text, true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. FUNÇÃO PARA OBTER TENANT ATUAL
-- =====================================================
CREATE OR REPLACE FUNCTION get_current_tenant_id()
RETURNS UUID AS $$
BEGIN
  RETURN COALESCE(
    current_setting('app.current_tenant_id', true)::UUID,
    NULL
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. HABILITAR ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Habilitar RLS nas tabelas
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_interaction_costs ENABLE ROW LEVEL SECURITY;
ALTER TABLE cotacoes_historicas_b3 ENABLE ROW LEVEL SECURITY;
ALTER TABLE curvas_b3 ENABLE ROW LEVEL SECURITY;
ALTER TABLE cri_cra_precos ENABLE ROW LEVEL SECURITY;
ALTER TABLE debentures_precos ENABLE ROW LEVEL SECURITY;
ALTER TABLE titulos_publicos_precos ENABLE ROW LEVEL SECURITY;
ALTER TABLE letras_precos ENABLE ROW LEVEL SECURITY;

-- 7. POLÍTICAS DE SEGURANÇA (RLS POLICIES)
-- =====================================================

-- Política para tabela tenants (apenas leitura para tenants ativos)
DROP POLICY IF EXISTS tenant_read_policy ON tenants;
CREATE POLICY tenant_read_policy ON tenants
  FOR SELECT USING (is_active = true);

-- Política para agent_interaction_costs
DROP POLICY IF EXISTS tenant_isolation_costs ON agent_interaction_costs;
CREATE POLICY tenant_isolation_costs ON agent_interaction_costs
  FOR ALL USING (
    tenant_id = get_current_tenant_id() OR 
    tenant_id IS NULL -- Para compatibilidade durante migração
  );

-- Política para cotacoes_historicas_b3
DROP POLICY IF EXISTS tenant_isolation_cotacoes ON cotacoes_historicas_b3;
CREATE POLICY tenant_isolation_cotacoes ON cotacoes_historicas_b3
  FOR ALL USING (
    tenant_id = get_current_tenant_id() OR 
    tenant_id IS NULL
  );

-- Política para curvas_b3
DROP POLICY IF EXISTS tenant_isolation_curvas ON curvas_b3;
CREATE POLICY tenant_isolation_curvas ON curvas_b3
  FOR ALL USING (
    tenant_id = get_current_tenant_id() OR 
    tenant_id IS NULL
  );

-- Política para cri_cra_precos
DROP POLICY IF EXISTS tenant_isolation_cri_cra ON cri_cra_precos;
CREATE POLICY tenant_isolation_cri_cra ON cri_cra_precos
  FOR ALL USING (
    tenant_id = get_current_tenant_id() OR 
    tenant_id IS NULL
  );

-- Política para debentures_precos
DROP POLICY IF EXISTS tenant_isolation_debentures ON debentures_precos;
CREATE POLICY tenant_isolation_debentures ON debentures_precos
  FOR ALL USING (
    tenant_id = get_current_tenant_id() OR 
    tenant_id IS NULL
  );

-- Política para titulos_publicos_precos
DROP POLICY IF EXISTS tenant_isolation_titulos ON titulos_publicos_precos;
CREATE POLICY tenant_isolation_titulos ON titulos_publicos_precos
  FOR ALL USING (
    tenant_id = get_current_tenant_id() OR 
    tenant_id IS NULL
  );

-- Política para letras_precos
DROP POLICY IF EXISTS tenant_isolation_letras ON letras_precos;
CREATE POLICY tenant_isolation_letras ON letras_precos
  FOR ALL USING (
    tenant_id = get_current_tenant_id() OR 
    tenant_id IS NULL
  );

-- 8. CRIAR TENANT DEMO INICIAL
-- =====================================================
INSERT INTO tenants (subdomain, name, primary_color, secondary_color, settings)
VALUES (
  'demo',
  'Empresa Demo',
  '#1f2937',
  '#f3f4f6',
  '{"features": {"chat": true, "analytics": true}}'
) ON CONFLICT (subdomain) DO NOTHING;

-- 9. TRIGGER PARA UPDATED_AT
-- =====================================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_tenants_updated_at ON tenants;
CREATE TRIGGER update_tenants_updated_at
    BEFORE UPDATE ON tenants
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 10. COMENTÁRIOS PARA DOCUMENTAÇÃO
-- =====================================================
COMMENT ON TABLE tenants IS 'Tabela de tenants para multi-tenancy';
COMMENT ON COLUMN tenants.subdomain IS 'Subdomínio único para o tenant (ex: empresa1 para empresa1.vascofa.shop)';
COMMENT ON COLUMN tenants.settings IS 'Configurações específicas do tenant em formato JSON';
COMMENT ON FUNCTION set_current_tenant(UUID) IS 'Define o tenant atual para Row Level Security';
COMMENT ON FUNCTION get_current_tenant_id() IS 'Retorna o ID do tenant atual definido no contexto';

-- =====================================================
-- FIM DO SETUP MULTI-TENANT
-- =====================================================
