import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const subdomain = request.headers.get('x-tenant-subdomain');
    
    if (!subdomain) {
      return NextResponse.json({ error: 'Tenant não encontrado' }, { status: 404 });
    }

    const supabase = await createClient();
    
    const { data: tenant, error } = await supabase
      .from('tenants')
      .select('id, subdomain, name, logo_url, primary_color, secondary_color, custom_css, settings')
      .eq('subdomain', subdomain)
      .eq('is_active', true)
      .single();

    if (error || !tenant) {
      return NextResponse.json({ error: 'Tenant não encontrado' }, { status: 404 });
    }

    return NextResponse.json({
      id: tenant.id,
      subdomain: tenant.subdomain,
      name: tenant.name,
      logoUrl: tenant.logo_url,
      primaryColor: tenant.primary_color,
      secondaryColor: tenant.secondary_color,
      customCss: tenant.custom_css,
      settings: tenant.settings
    });
  } catch (error) {
    console.error('Erro ao buscar tenant:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}
