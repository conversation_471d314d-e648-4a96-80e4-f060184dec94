-- =====================================================
-- VERIFICAR E CRIAR USUÁRIO NA TABELA APP.USERS
-- Execute no Supabase SQL Editor
-- =====================================================

-- 1. VERIFICAR SE USUÁRIO EXISTE EM AUTH.USERS
-- =====================================================
SELECT 
    'AUTH.USERS' as tabela,
    id,
    email,
    created_at,
    raw_user_meta_data
FROM auth.users 
WHERE id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d';

-- 2. VERIFICAR SE USUÁRIO EXISTE EM APP.USERS
-- =====================================================
SELECT 
    'APP.USERS' as tabela,
    user_id,
    full_name,
    email,
    tenant_id,
    credits,
    created_at
FROM app.users 
WHERE user_id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d';

-- 3. VERIFICAR TENANT VASCO
-- =====================================================
SELECT 
    'TENANT VASCO' as info,
    id,
    subdomain,
    name,
    is_active
FROM app.tenants 
WHERE subdomain = 'vasco';

-- 4. CRIAR USUÁRIO EM APP.USERS SE NÃO EXISTIR
-- =====================================================
DO $$
DECLARE
    vasco_tenant_id UUID;
    user_exists BOOLEAN;
    auth_user_data RECORD;
BEGIN
    -- Buscar ID do tenant Vasco
    SELECT id INTO vasco_tenant_id 
    FROM app.tenants 
    WHERE subdomain = 'vasco' AND is_active = true;
    
    IF vasco_tenant_id IS NULL THEN
        RAISE EXCEPTION 'Tenant Vasco não encontrado ou inativo';
    END IF;
    
    RAISE NOTICE 'Tenant Vasco ID: %', vasco_tenant_id;
    
    -- Verificar se usuário existe em app.users
    SELECT EXISTS(
        SELECT 1 FROM app.users 
        WHERE user_id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d'
    ) INTO user_exists;
    
    IF user_exists THEN
        RAISE NOTICE 'Usuário já existe em app.users';
        
        -- Atualizar tenant_id se necessário
        UPDATE app.users 
        SET tenant_id = vasco_tenant_id,
            updated_at = NOW()
        WHERE user_id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d'
        AND (tenant_id IS NULL OR tenant_id != vasco_tenant_id);
        
        RAISE NOTICE 'Tenant_id atualizado se necessário';
    ELSE
        -- Buscar dados do auth.users
        SELECT * INTO auth_user_data
        FROM auth.users 
        WHERE id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d';
        
        IF auth_user_data IS NULL THEN
            RAISE EXCEPTION 'Usuário não encontrado em auth.users';
        END IF;
        
        -- Criar usuário em app.users
        INSERT INTO app.users (
            user_id,
            full_name,
            email,
            tenant_id,
            credits,
            created_at,
            updated_at
        ) VALUES (
            auth_user_data.id,
            COALESCE(auth_user_data.raw_user_meta_data->>'full_name', 'Usuário'),
            auth_user_data.email,
            vasco_tenant_id,
            100, -- Créditos iniciais
            auth_user_data.created_at,
            NOW()
        );
        
        RAISE NOTICE 'Usuário criado em app.users com tenant_id: %', vasco_tenant_id;
    END IF;
END $$;

-- 5. VERIFICAR RESULTADO FINAL
-- =====================================================
SELECT 
    'VERIFICAÇÃO FINAL' as status,
    u.user_id,
    u.full_name,
    u.email,
    u.tenant_id,
    u.credits,
    t.subdomain,
    t.name as tenant_name
FROM app.users u
JOIN app.tenants t ON u.tenant_id = t.id
WHERE u.user_id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d';

-- 6. TESTAR ACESSO COM CONTEXTO
-- =====================================================
-- Definir contexto do tenant Vasco
SELECT app.set_current_tenant('1c11dd1a-97a8-45fd-a295-f56963f50f9a');

-- Testar query que estava falhando
SELECT 
    'TESTE COM CONTEXTO' as teste,
    user_id,
    tenant_id,
    full_name,
    email,
    credits
FROM app.users 
WHERE user_id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d';

-- 7. VERIFICAR TRIGGER HANDLE_NEW_USER
-- =====================================================
SELECT 
    'TRIGGER CHECK' as info,
    trigger_name,
    event_manipulation,
    action_timing,
    action_statement
FROM information_schema.triggers 
WHERE event_object_schema = 'auth'
AND event_object_table = 'users'
AND trigger_name LIKE '%new_user%';

-- 8. VERIFICAR TODOS OS USUÁRIOS ÓRFÃOS
-- =====================================================
SELECT 
    'USUÁRIOS ÓRFÃOS' as problema,
    au.id as auth_user_id,
    au.email as auth_email,
    au.created_at as auth_created,
    u.user_id as app_user_id
FROM auth.users au
LEFT JOIN app.users u ON au.id = u.user_id
WHERE u.user_id IS NULL
ORDER BY au.created_at DESC;

-- 9. CRIAR TODOS OS USUÁRIOS ÓRFÃOS NO TENANT VASCO
-- =====================================================
DO $$
DECLARE
    vasco_tenant_id UUID;
    orphan_user RECORD;
    created_count INTEGER := 0;
BEGIN
    -- Buscar ID do tenant Vasco
    SELECT id INTO vasco_tenant_id 
    FROM app.tenants 
    WHERE subdomain = 'vasco' AND is_active = true;
    
    -- Criar usuários órfãos
    FOR orphan_user IN 
        SELECT au.id, au.email, au.created_at, au.raw_user_meta_data
        FROM auth.users au
        LEFT JOIN app.users u ON au.id = u.user_id
        WHERE u.user_id IS NULL
    LOOP
        INSERT INTO app.users (
            user_id,
            full_name,
            email,
            tenant_id,
            credits,
            created_at,
            updated_at
        ) VALUES (
            orphan_user.id,
            COALESCE(orphan_user.raw_user_meta_data->>'full_name', 'Usuário'),
            orphan_user.email,
            vasco_tenant_id,
            100,
            orphan_user.created_at,
            NOW()
        );
        
        created_count := created_count + 1;
    END LOOP;
    
    RAISE NOTICE 'Criados % usuários órfãos no tenant Vasco', created_count;
END $$;

-- 10. VERIFICAÇÃO FINAL COMPLETA
-- =====================================================
SELECT 
    'RESUMO FINAL' as info,
    COUNT(*) as total_users_app,
    COUNT(CASE WHEN tenant_id = '1c11dd1a-97a8-45fd-a295-f56963f50f9a' THEN 1 END) as users_vasco
FROM app.users;

-- =====================================================
-- RESULTADO ESPERADO:
-- - Usuário existe em auth.users
-- - Usuário criado/atualizado em app.users
-- - Tenant_id correto (Vasco)
-- - Query com contexto retorna dados
-- =====================================================
