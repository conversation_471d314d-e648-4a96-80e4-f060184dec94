-- =====================================================
-- CORREÇÃO PARA REGISTRO DE USUÁRIOS COM TENANT_ID
-- =====================================================

-- 1. VERIFICAR E CORRIGIR ESTRUTURA DA TABELA app.users
-- =====================================================

-- Verificar se a tabela users existe e sua estrutura
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_schema = 'app' AND table_name = 'users'
ORDER BY ordinal_position;

-- Adicionar tenant_id à tabela users se não existir
ALTER TABLE app.users 
ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES app.tenants(id);

-- Remover constraint NOT NULL temporariamente se existir
ALTER TABLE app.users 
ALTER COLUMN tenant_id DROP NOT NULL;

-- 2. FUNÇÃO PARA OBTER TENANT ATUAL DO CONTEXTO
-- =====================================================

-- Função para obter tenant_id baseado no subdomínio atual
CREATE OR REPLACE FUNCTION get_tenant_from_subdomain(subdomain_param TEXT)
RETURNS UUID AS $$
DECLARE
    tenant_uuid UUID;
BEGIN
    -- Buscar tenant pelo subdomínio
    SELECT id INTO tenant_uuid
    FROM app.tenants 
    WHERE subdomain = subdomain_param 
    AND is_active = true;
    
    RETURN tenant_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. FUNÇÃO ATUALIZADA PARA TRIGGER DE USUÁRIOS
-- =====================================================

CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    current_tenant_id UUID;
    user_subdomain TEXT;
BEGIN
    -- Tentar obter tenant_id do contexto RLS primeiro
    BEGIN
        current_tenant_id := get_current_tenant_id();
    EXCEPTION WHEN OTHERS THEN
        current_tenant_id := NULL;
    END;
    
    -- Se não há tenant no contexto, tentar obter do metadata do usuário
    IF current_tenant_id IS NULL THEN
        -- Verificar se há subdomain no metadata
        user_subdomain := NEW.raw_user_meta_data->>'subdomain';
        
        IF user_subdomain IS NOT NULL THEN
            current_tenant_id := get_tenant_from_subdomain(user_subdomain);
        END IF;
    END IF;
    
    -- Se ainda não há tenant, usar um tenant padrão ou criar erro
    IF current_tenant_id IS NULL THEN
        -- Buscar tenant padrão (demo)
        SELECT id INTO current_tenant_id
        FROM app.tenants 
        WHERE subdomain = 'demo' 
        AND is_active = true
        LIMIT 1;
        
        -- Se não há tenant demo, usar o primeiro tenant ativo
        IF current_tenant_id IS NULL THEN
            SELECT id INTO current_tenant_id
            FROM app.tenants 
            WHERE is_active = true
            ORDER BY created_at ASC
            LIMIT 1;
        END IF;
    END IF;
    
    -- Inserir usuário com tenant_id
    INSERT INTO app.users (user_id, full_name, email, tenant_id)
    VALUES (
        NEW.id,
        NEW.raw_user_meta_data->>'full_name',
        NEW.email,
        current_tenant_id
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. RECRIAR TRIGGER SE NECESSÁRIO
-- =====================================================

-- Remover trigger existente se houver
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Criar novo trigger
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION handle_new_user();

-- 5. ATUALIZAR TABELA agent_interaction_costs
-- =====================================================

-- Verificar se tenant_id já existe na tabela
ALTER TABLE app.agent_interaction_costs 
ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES app.tenants(id);

-- Criar índice para performance
CREATE INDEX IF NOT EXISTS idx_agent_costs_tenant_user 
ON app.agent_interaction_costs(tenant_id, user_id);

-- 6. FUNÇÃO PARA INSERIR CUSTOS COM TENANT_ID AUTOMÁTICO
-- =====================================================

CREATE OR REPLACE FUNCTION insert_agent_cost(
    p_user_id UUID,
    p_session_id TEXT,
    p_invocation_id TEXT,
    p_agent_author TEXT,
    p_prompt_token_count INTEGER,
    p_candidates_token_count INTEGER,
    p_thoughts_token_count INTEGER,
    p_total_cost_usd DECIMAL,
    p_metadata JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    cost_id UUID;
    current_tenant_id UUID;
BEGIN
    -- Obter tenant_id do usuário
    SELECT tenant_id INTO current_tenant_id
    FROM app.users
    WHERE user_id = p_user_id;
    
    -- Se não encontrou, tentar do contexto RLS
    IF current_tenant_id IS NULL THEN
        current_tenant_id := get_current_tenant_id();
    END IF;
    
    -- Inserir registro de custo
    INSERT INTO app.agent_interaction_costs (
        user_id,
        session_id,
        invocation_id,
        agent_author,
        prompt_token_count,
        candidates_token_count,
        thoughts_token_count,
        total_cost_usd,
        metadata,
        tenant_id
    ) VALUES (
        p_user_id,
        p_session_id,
        p_invocation_id,
        p_agent_author,
        p_prompt_token_count,
        p_candidates_token_count,
        p_thoughts_token_count,
        p_total_cost_usd,
        p_metadata,
        current_tenant_id
    ) RETURNING id INTO cost_id;
    
    RETURN cost_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. ATUALIZAR USUÁRIOS EXISTENTES SEM TENANT_ID
-- =====================================================

-- Atualizar usuários existentes para usar tenant demo
UPDATE app.users 
SET tenant_id = (
    SELECT id FROM app.tenants 
    WHERE subdomain = 'demo' 
    AND is_active = true 
    LIMIT 1
)
WHERE tenant_id IS NULL;

-- 8. VERIFICAÇÕES FINAIS
-- =====================================================

-- Verificar se todos os usuários têm tenant_id
SELECT 
    COUNT(*) as total_users,
    COUNT(tenant_id) as users_with_tenant,
    COUNT(*) - COUNT(tenant_id) as users_without_tenant
FROM app.users;

-- Verificar tenants disponíveis
SELECT id, subdomain, name, is_active FROM app.tenants ORDER BY created_at;

-- =====================================================
-- COMENTÁRIOS E DOCUMENTAÇÃO
-- =====================================================

COMMENT ON FUNCTION handle_new_user() IS 'Trigger function para criar usuário com tenant_id baseado no contexto ou metadata';
COMMENT ON FUNCTION get_tenant_from_subdomain(TEXT) IS 'Busca tenant_id pelo subdomínio';
COMMENT ON FUNCTION insert_agent_cost(UUID, TEXT, TEXT, TEXT, INTEGER, INTEGER, INTEGER, DECIMAL, JSONB) IS 'Insere custo de interação com tenant_id automático';

-- =====================================================
-- FIM DA CORREÇÃO
-- =====================================================
