"use client";

import { useEffect, useState } from "react";

interface MobileSafeProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * Componente específico para resolver problemas de hidratação no mobile
 * Baseado nas melhores práticas do Next.js para evitar diferenças servidor/cliente
 */
export function MobileSafe({ children, fallback = null }: MobileSafeProps) {
  const [isClient, setIsClient] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsClient(true);
    // Aguardar um tick adicional para garantir que tudo está hidratado
    const timer = setTimeout(() => {
      setIsMounted(true);
    }, 0);

    return () => clearTimeout(timer);
  }, []);

  // Durante SSR e hidratação inicial, mostrar fallback
  if (!isClient || !isMounted) {
    return <div suppressHydrationWarning>{fallback}</div>;
  }

  // Após hidratação completa, mostrar conteúdo real
  return <div suppressHydrationWarning>{children}</div>;
}
