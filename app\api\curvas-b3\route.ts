import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

// Definindo os headers CORS como constante (não exportada)
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, apikey'
};

export async function OPTIONS() {
  return NextResponse.json({}, { headers: corsHeaders });
}

export async function GET(request: NextRequest) {
  // Verificar autenticação
  const authHeader = request.headers.get('Authorization');

  if (!authHeader) {
    return NextResponse.json(
      { error: 'Cabeçalho de autorização não fornecido' },
      { status: 401, headers: corsHeaders }
    );
  }

  const token = authHeader.replace('Bearer ', '');
  const supabase = await createClient();

  // Verificar o usuário
  const { data: { user }, error: userError } = await supabase.auth.getUser(token);

  if (userError || !user) {
    return NextResponse.json(
      { error: 'Token inválido' },
      { status: 401, headers: corsHeaders }
    );
  }

  // Obter o parâmetro data_referencia, se fornecido
  const url = new URL(request.url);
  const dataReferencia = url.searchParams.get('data_referencia');

  // Construir a consulta para curvas_b3
  let query = supabase
    .from('curvas_b3')
    .select('caracteristica_vertice, cod_curvas_termo, cod_taxa, cod_vertice, complemento_transacao, desc_taxa, dias_corridos_taxa_juro, dt_geracao_arquivo, id_transacao, saques_taxa_juro, sinal_taxa_teorica, taxa_teorica, tipo_registro');

  // Adicionar filtro por data_referencia, se fornecido
  if (dataReferencia) {
    query = query.eq('dt_geracao_arquivo', dataReferencia);
  }

  const { data, error } = await query;

  if (error) {
    return NextResponse.json(
      { error: error.message },
      { status: 500, headers: corsHeaders }
    );
  }

  return NextResponse.json(
    { data },
    { headers: corsHeaders }
  );
}