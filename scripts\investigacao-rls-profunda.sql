-- =====================================================
-- INVESTIGAÇÃO PROFUNDA DO PROBLEMA RLS
-- Execute no Supabase SQL Editor
-- =====================================================

-- 1. VERIFICAR CONFIGURAÇÃO ATUAL DAS FUNÇÕES
-- =====================================================
SELECT 
    'FUNÇÕES RLS' as check_type,
    routine_name,
    routine_schema,
    routine_definition,
    security_type
FROM information_schema.routines 
WHERE routine_schema = 'app' 
AND routine_name IN ('set_current_tenant', 'get_current_tenant_id');

-- 2. TESTAR PROBLEMA DO CONTEXTO HTTP
-- =====================================================

-- Simular o problema: contexto não persiste entre chamadas
DO $$
BEGIN
    -- Limpar contexto
    PERFORM set_config('app.current_tenant_id', '', false);
    RAISE NOTICE 'Contexto limpo: %', current_setting('app.current_tenant_id', true);
    
    -- Definir contexto
    PERFORM app.set_current_tenant('1c11dd1a-97a8-45fd-a295-f56963f50f9a');
    RAISE NOTICE 'Contexto definido: %', current_setting('app.current_tenant_id', true);
    
    -- Verificar função
    RAISE NOTICE 'Função retorna: %', app.get_current_tenant_id();
    
    -- Testar se string vazia causa problema
    PERFORM set_config('app.current_tenant_id', '', false);
    RAISE NOTICE 'Contexto vazio - Função retorna: %', app.get_current_tenant_id();
    RAISE NOTICE 'Contexto vazio - Setting retorna: "%"', current_setting('app.current_tenant_id', true);
END $$;

-- 3. VERIFICAR POLÍTICA RLS ATUAL
-- =====================================================
SELECT 
    'POLÍTICA ATUAL' as info,
    policyname,
    qual as condition,
    cmd as command
FROM pg_policies 
WHERE schemaname = 'app' 
AND tablename = 'users';

-- 4. TESTAR POLÍTICA COM DIFERENTES VALORES
-- =====================================================

-- Teste 1: Com UUID válido
SELECT app.set_current_tenant('1c11dd1a-97a8-45fd-a295-f56963f50f9a');
SELECT 
    'TESTE UUID VÁLIDO' as teste,
    COUNT(*) as count,
    app.get_current_tenant_id() as context
FROM app.users;

-- Teste 2: Com string vazia (simula problema)
SELECT set_config('app.current_tenant_id', '', false);
SELECT 
    'TESTE STRING VAZIA' as teste,
    COUNT(*) as count,
    app.get_current_tenant_id() as context
FROM app.users;

-- Teste 3: Com NULL
SELECT set_config('app.current_tenant_id', NULL, false);
SELECT 
    'TESTE NULL' as teste,
    COUNT(*) as count,
    app.get_current_tenant_id() as context
FROM app.users;

-- 5. VERIFICAR SE PROBLEMA É NA FUNÇÃO GET_CURRENT_TENANT_ID
-- =====================================================

-- Testar conversão de string vazia para UUID
DO $$
BEGIN
    -- Definir string vazia
    PERFORM set_config('app.current_tenant_id', '', false);
    
    -- Testar conversão direta
    BEGIN
        RAISE NOTICE 'Tentando converter string vazia para UUID...';
        RAISE NOTICE 'Resultado: %', current_setting('app.current_tenant_id', true)::UUID;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'ERRO na conversão: %', SQLERRM;
    END;
    
    -- Testar função COALESCE
    RAISE NOTICE 'COALESCE com string vazia: %', COALESCE(current_setting('app.current_tenant_id', true)::UUID, NULL);
END $$;

-- 6. CRIAR FUNÇÃO CORRIGIDA
-- =====================================================

-- Função corrigida que trata string vazia
CREATE OR REPLACE FUNCTION app.get_current_tenant_id_fixed()
RETURNS UUID AS $$
DECLARE
    tenant_setting TEXT;
BEGIN
    -- Obter setting
    tenant_setting := current_setting('app.current_tenant_id', true);
    
    -- Verificar se é string vazia ou NULL
    IF tenant_setting IS NULL OR tenant_setting = '' THEN
        RETURN NULL;
    END IF;
    
    -- Tentar converter para UUID
    BEGIN
        RETURN tenant_setting::UUID;
    EXCEPTION WHEN OTHERS THEN
        RETURN NULL;
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. TESTAR FUNÇÃO CORRIGIDA
-- =====================================================

-- Teste com string vazia
SELECT set_config('app.current_tenant_id', '', false);
SELECT 
    'FUNÇÃO ORIGINAL' as tipo,
    app.get_current_tenant_id() as resultado;

SELECT 
    'FUNÇÃO CORRIGIDA' as tipo,
    app.get_current_tenant_id_fixed() as resultado;

-- Teste com UUID válido
SELECT app.set_current_tenant('1c11dd1a-97a8-45fd-a295-f56963f50f9a');
SELECT 
    'FUNÇÃO ORIGINAL (UUID)' as tipo,
    app.get_current_tenant_id() as resultado;

SELECT 
    'FUNÇÃO CORRIGIDA (UUID)' as tipo,
    app.get_current_tenant_id_fixed() as resultado;

-- 8. CRIAR POLÍTICA RLS CORRIGIDA
-- =====================================================

-- Remover política atual
DROP POLICY IF EXISTS tenant_isolation_users ON app.users;

-- Criar política que trata NULL corretamente
CREATE POLICY tenant_isolation_users_fixed ON app.users
  FOR ALL USING (
    -- Permitir acesso apenas se tenant_id corresponde ao contexto
    -- E o contexto não é NULL (evita acesso quando contexto não definido)
    tenant_id = app.get_current_tenant_id_fixed() 
    AND app.get_current_tenant_id_fixed() IS NOT NULL
  );

-- 9. TESTAR POLÍTICA CORRIGIDA
-- =====================================================

-- Teste 1: Sem contexto (deve retornar 0)
SELECT set_config('app.current_tenant_id', '', false);
SELECT 
    'SEM CONTEXTO - POLÍTICA CORRIGIDA' as teste,
    COUNT(*) as count
FROM app.users;

-- Teste 2: Com contexto válido (deve retornar dados)
SELECT app.set_current_tenant('1c11dd1a-97a8-45fd-a295-f56963f50f9a');
SELECT 
    'COM CONTEXTO - POLÍTICA CORRIGIDA' as teste,
    COUNT(*) as count,
    array_agg(user_id) as user_ids
FROM app.users;

-- 10. VERIFICAR PERMISSÕES DO ROLE ANON
-- =====================================================

-- Verificar se anon pode executar as funções
SELECT 
    'PERMISSÕES FUNÇÃO' as check_type,
    routine_name,
    routine_schema,
    security_type,
    CASE 
        WHEN security_type = 'DEFINER' THEN 'OK - Security Definer'
        ELSE 'PROBLEMA - Não é Security Definer'
    END as status
FROM information_schema.routines 
WHERE routine_schema = 'app' 
AND routine_name IN ('set_current_tenant', 'get_current_tenant_id');

-- 11. SOLUÇÃO ALTERNATIVA: POLÍTICA BASEADA EM HEADER
-- =====================================================

-- Criar política que usa header diretamente (para teste)
CREATE POLICY tenant_isolation_users_header ON app.users
  FOR ALL USING (
    tenant_id::text = current_setting('request.headers', true)::json->>'x-tenant-id'
  );

-- Desabilitar política anterior para testar
ALTER TABLE app.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE app.users ENABLE ROW LEVEL SECURITY;

-- 12. DIAGNÓSTICO FINAL
-- =====================================================
SELECT 
    'DIAGNÓSTICO FINAL' as secao,
    'Verificar resultados dos testes acima' as instrucao;

-- Verificar se usuário específico existe
SELECT 
    'USUÁRIO EXISTE?' as check_type,
    user_id,
    tenant_id,
    full_name
FROM app.users 
WHERE user_id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d';

-- =====================================================
-- RESULTADO ESPERADO:
-- - Função original falha com string vazia
-- - Função corrigida trata string vazia
-- - Política corrigida bloqueia acesso sem contexto
-- - Política corrigida permite acesso com contexto
-- =====================================================
