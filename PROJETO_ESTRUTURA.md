# 📋 PrimeAI - Documentação Completa do Projeto

## 🎯 Visão Geral

**PrimeAI** é um assistente financeiro inteligente desenvolvido em Next.js 15 com integração ao Google Vertex AI. O sistema oferece chat interativo, análise de dados financeiros, visualização de gráficos e dashboard de uso.

### 🏗️ Arquitetura Principal

```
Frontend (Next.js) ↔ Supabase (Auth/DB) ↔ Google Vertex AI ↔ APIs Financeiras
```

---

## 📁 Estrutura de Pastas

### 🔧 **Configurações Raiz**
```
├── package.json              # Dependências e scripts
├── next.config.js            # Configuração do Next.js
├── tailwind.config.ts        # Configuração do Tailwind CSS
├── tsconfig.json             # Configuração do TypeScript
├── components.json           # Configuração do shadcn/ui
└── .env.local               # Variáveis de ambiente (não commitado)
```

### 📱 **App Directory (Next.js 15)**
```
app/
├── layout.tsx               # Layout raiz da aplicação
├── globals.css              # Estilos globais
├── favicon.ico              # Ícone da aplicação
├── (public)/                # Grupo de rotas públicas
│   ├── layout.tsx           # Layout para páginas públicas
│   ├── page.tsx             # Homepage
│   ├── sign-in/             # Página de login
│   ├── sign-up/             # Página de registro
│   └── forgot-password/     # Recuperação de senha
├── chat/                    # Área protegida - Chat
│   ├── layout.tsx           # Layout com sidebar e auth
│   ├── page.tsx             # Interface principal do chat
│   └── reset-password/      # Reset de senha
├── uso/                     # Área protegida - Dashboard de uso
│   ├── layout.tsx           # Layout com sidebar e auth
│   └── page.tsx             # Dashboard de tokens/mensagens
├── api/                     # API Routes
│   ├── chat/                # Endpoint principal do chat
│   ├── uso/                 # API de dados de uso
│   ├── session/             # Gerenciamento de sessões
│   ├── costs/               # Logging de custos
│   └── [financial-apis]/    # APIs de dados financeiros
└── auth/
    └── callback/            # Callback do Supabase Auth
```

### 🧩 **Components**
```
components/
├── ui/                      # Componentes base (shadcn/ui)
│   ├── button.tsx
│   ├── input.tsx
│   ├── card.tsx
│   └── [outros...]
├── auth/                    # Componentes de autenticação
│   ├── signin-form-client.tsx
│   ├── signup-form-client.tsx
│   └── static-forms.tsx
├── chat/                    # Componentes do chat
│   ├── chat-interface.tsx   # Interface principal
│   ├── chat-message.tsx     # Renderização de mensagens
│   ├── chat-sidebar.tsx     # Sidebar de conversas
│   ├── session-context.tsx  # Context de sessões
│   ├── suggestion-cards.tsx # Cards de sugestões
│   └── typing-indicator.tsx # Indicador de digitação
├── uso/                     # Componentes do dashboard
│   ├── UsoStats.tsx         # Cards de estatísticas
│   ├── UsoFilters.tsx       # Filtros de período
│   ├── TokensOnlyChart.tsx  # Gráfico de tokens
│   └── MessagesChart.tsx    # Gráfico de mensagens
├── export/                  # Sistema de exportação
│   └── export-modal/        # Modal de exportação
├── providers/               # Providers globais
│   ├── toast-provider.tsx
│   └── console-security-provider.tsx
├── main-sidebar.tsx         # Sidebar principal
└── header-auth-wrapper.tsx  # Header de autenticação
```

### 🛠️ **Utilitários e Configurações**
```
lib/
├── utils.ts                 # Utilitários gerais
├── agent-cost-logger.ts     # Logger de custos do agente
├── config/
│   └── database-config.ts   # Configuração de bases de dados
└── utils/
    ├── console-interceptor.ts # Interceptador de logs
    └── secure-logger.ts      # Sistema de logging seguro

utils/
└── supabase/
    ├── client.ts            # Cliente Supabase (browser)
    ├── server.ts            # Cliente Supabase (server)
    └── middleware.ts        # Middleware de autenticação

types/
└── [definições de tipos TypeScript]
```

---

## 🔐 Autenticação e Segurança

### **Sistema de Auth (Supabase)**
- **Login/Registro**: Email + senha
- **Recuperação**: Reset de senha por email
- **Sessões**: Persistentes com refresh automático
- **Middleware**: Proteção de rotas automática

### **Segurança Implementada**
- **Console Interceptor**: Suprime logs sensíveis em produção
- **Secure Logger**: Sistema de logging que mascara dados sensíveis
- **Environment Variables**: Separação de configs dev/prod
- **CORS**: Configurado para domínios específicos

---

## 💬 Sistema de Chat

### **Fluxo Principal**
1. **Criação de Sessão**: Usuário clica "Nova Conversa"
2. **Envio de Mensagem**: Interface envia para `/api/chat`
3. **Processamento**: API roteia para Vertex AI ou servidor local
4. **Resposta**: Streaming ou resposta única
5. **Logging**: Custos salvos automaticamente

### **Componentes Principais**
- **ChatInterface**: Gerencia estado e UI
- **SessionContext**: Context para sessões ativas
- **ChatMessage**: Renderiza mensagens com markdown/charts
- **ChatSidebar**: Lista de conversas do usuário

### **Recursos**
- **Markdown**: Suporte completo com `marked`
- **Gráficos**: ECharts integrado para visualizações
- **Mobile**: Interface responsiva
- **Typing**: Indicador de digitação animado
- **Sugestões**: Cards de prompts predefinidos

---

## 📊 Dashboard de Uso

### **Funcionalidades**
- **Estatísticas**: Total de tokens, mensagens, custos
- **Filtros**: Por período (dia/semana/mês)
- **Gráficos**: Tokens e mensagens separados
- **Agrupamento**: Por `invocation_id` (conversas únicas)

### **Processamento de Dados**
1. **Query**: Busca dados da tabela `agent_interaction_costs`
2. **Agrupamento**: Por `invocation_id` para contar conversas
3. **Timezone**: Respeita São Paulo (-03:00)
4. **Visualização**: ECharts com Recharts

---

## 🔌 APIs e Integrações

### **APIs Internas**
- **`/api/chat`**: Endpoint principal do chat
- **`/api/uso`**: Dados de uso e custos
- **`/api/session`**: Gerenciamento de sessões
- **`/api/costs`**: Logging de custos

### **APIs Financeiras**
- **B3**: Curvas de juros
- **CRI/CRA**: Preços de certificados
- **Debêntures**: Dados de debêntures
- **Títulos Públicos**: Preços do Tesouro
- **Letras**: LCI, LCA, etc.

### **Integrações Externas**
- **Google Vertex AI**: Processamento de IA
- **Supabase**: Auth + Database
- **Vercel Analytics**: Métricas de uso

---

## 🗄️ Banco de Dados

### **Schema Principal (Supabase)**
```sql
-- Tabela de custos (schema: app)
agent_interaction_costs (
  id: uuid PRIMARY KEY,
  user_id: uuid REFERENCES auth.users,
  session_id: text,
  invocation_id: text,
  agent_author: text,
  prompt_token_count: integer,
  candidates_token_count: integer,
  thoughts_token_count: integer,
  total_cost_usd: decimal,
  response_timestamp: timestamptz,
  metadata: jsonb
)
```

### **Timezone**
- **Configuração**: São Paulo (-03:00)
- **Queries**: Filtros respeitam timezone local
- **Display**: Datas formatadas em pt-BR

---

## 🎨 UI/UX

### **Design System**
- **Framework**: Tailwind CSS
- **Componentes**: shadcn/ui
- **Tema**: Dark/Light mode automático
- **Responsivo**: Mobile-first approach

### **Layout Principal**
```
┌─────────────────────────────────────────┐
│ [≡] PrimeAI    │ Área de Conteúdo      │
│                │                       │
│ 💬 Chat        │ [Chat Interface]      │
│ 💳 Uso         │ ou                    │
│                │ [Dashboard de Uso]    │
│ <EMAIL> │                       │
│ 🚪 Sair        │                       │
└─────────────────────────────────────────┘
```

### **Responsividade**
- **Desktop**: Sidebar fixa + conteúdo
- **Mobile**: Sidebar colapsável + overlay
- **Breakpoints**: Tailwind padrão

---

## ⚙️ Configuração de Desenvolvimento

### **Variáveis de Ambiente Necessárias**
```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# Google Cloud / Vertex AI
GOOGLE_APPLICATION_CREDENTIALS=
GOOGLE_CLOUD_PROJECT_ID=

# Chat Environment
CHAT_ENVIRONMENT=local|production
LOCAL_AI_SERVER_URL=http://localhost:8000
LOCAL_AGENT_NAME=financial-agent

# Controles de Funcionalidades
ENABLE_SIGNUP=true                         # true/false - Habilita/desabilita registro
CHAT_MESSAGE_MAX_CHARS=5000               # Número máximo de caracteres por mensagem

# Opcional
NEXT_PUBLIC_AGENT_API_URL=
```

### **Scripts Disponíveis**
```bash
npm run dev      # Desenvolvimento
npm run build    # Build de produção
npm run start    # Servidor de produção
```

### **Dependências Principais**
- **Next.js 15**: Framework React
- **React 18**: Biblioteca UI
- **TypeScript**: Tipagem estática
- **Tailwind CSS**: Styling
- **Supabase**: Backend-as-a-Service
- **ECharts**: Gráficos interativos
- **Recharts**: Gráficos complementares
- **Marked**: Processamento de Markdown

---

## 🚀 Deploy e Produção

### **Plataforma Recomendada**
- **Vercel**: Deploy automático
- **Supabase**: Database + Auth
- **Google Cloud**: Vertex AI

### **Checklist de Produção**
- ✅ Variáveis de ambiente configuradas
- ✅ Logs de debug removidos
- ✅ Console interceptor ativo
- ✅ CORS configurado
- ✅ Analytics integrado

### **Monitoramento**
- **Vercel Analytics**: Métricas de performance
- **Supabase Dashboard**: Database e Auth
- **Console Logs**: Apenas erros críticos

---

## 🔧 Manutenção e Troubleshooting

### **Logs Importantes**
- **Custos**: Salvos automaticamente na tabela
- **Erros**: Console apenas em desenvolvimento
- **Performance**: Vercel Analytics

### **Problemas Comuns**
1. **Auth**: Verificar URLs do Supabase
2. **Chat**: Confirmar Vertex AI credentials
3. **Gráficos**: Validar formato de dados
4. **Mobile**: Testar responsividade

### **Backup e Segurança**
- **Database**: Backup automático Supabase
- **Código**: Git + GitHub
- **Secrets**: Nunca commitados

---

## 📞 Contato e Suporte

Para dúvidas sobre o projeto:
1. Consulte esta documentação
2. Verifique logs de erro
3. Teste em ambiente de desenvolvimento
4. Contate o time de desenvolvimento

---

**Última atualização**: Janeiro 2025
**Versão**: 1.0.0
**Ambiente**: Produção Ready ✅
