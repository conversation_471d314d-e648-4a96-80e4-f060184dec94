-- =====================================================
-- DIAGNÓSTICO COMPLETO DO PROJETO SUPABASE
-- Execute no Supabase SQL Editor
-- Project ID: opawvlqvsbltimqlzbdo
-- =====================================================

-- 1. INFORMAÇÕES GERAIS DO PROJETO
-- =====================================================
SELECT 
    'INFORMAÇÕES GERAIS' as secao,
    current_database() as database_name,
    current_user as current_user,
    session_user as session_user,
    current_setting('server_version') as postgres_version;

-- 2. VERIFICAR SCHEMAS DISPONÍVEIS
-- =====================================================
SELECT 
    'SCHEMAS' as secao,
    schema_name,
    schema_owner
FROM information_schema.schemata 
WHERE schema_name IN ('public', 'app', 'auth', 'storage', 'realtime')
ORDER BY schema_name;

-- 3. VERIFICAR TABELAS NO SCHEMA APP
-- =====================================================
SELECT 
    'TABELAS SCHEMA APP' as secao,
    table_name,
    table_type,
    is_insertable_into,
    is_typed
FROM information_schema.tables 
WHERE table_schema = 'app'
ORDER BY table_name;

-- 4. VERIFICAR ESTRUTURA DA TABELA USERS
-- =====================================================
SELECT 
    'ESTRUTURA TABELA USERS' as secao,
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_schema = 'app' 
AND table_name = 'users'
ORDER BY ordinal_position;

-- 5. VERIFICAR FUNÇÕES RLS
-- =====================================================
SELECT 
    'FUNÇÕES RLS' as secao,
    routine_name,
    routine_schema,
    routine_type,
    security_type,
    routine_definition
FROM information_schema.routines 
WHERE routine_schema = 'app' 
AND routine_name IN ('set_current_tenant', 'get_current_tenant_id')
ORDER BY routine_name;

-- 6. VERIFICAR POLÍTICAS RLS
-- =====================================================
SELECT 
    'POLÍTICAS RLS' as secao,
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual as condition
FROM pg_policies 
WHERE schemaname = 'app'
ORDER BY tablename, policyname;

-- 7. VERIFICAR STATUS RLS DAS TABELAS
-- =====================================================
SELECT 
    'STATUS RLS' as secao,
    schemaname,
    tablename,
    rowsecurity as rls_enabled,
    hasrules,
    hastriggers
FROM pg_tables 
WHERE schemaname = 'app'
ORDER BY tablename;

-- 8. VERIFICAR DADOS EXISTENTES
-- =====================================================

-- Contar registros nas tabelas principais
SELECT 'CONTAGEM TENANTS' as secao, COUNT(*) as total FROM app.tenants;
SELECT 'CONTAGEM USERS' as secao, COUNT(*) as total FROM app.users;

-- Verificar tenant específico
SELECT 
    'TENANT VASCO' as secao,
    id,
    subdomain,
    name,
    is_active,
    created_at
FROM app.tenants 
WHERE subdomain = 'vasco';

-- Verificar usuário específico
SELECT 
    'USUÁRIO ESPECÍFICO' as secao,
    user_id,
    full_name,
    email,
    tenant_id,
    credits,
    created_at
FROM app.users 
WHERE user_id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d';

-- 9. TESTAR FUNÇÕES RLS
-- =====================================================

-- Teste 1: Função get_current_tenant_id sem contexto
SELECT 
    'TESTE FUNÇÃO SEM CONTEXTO' as teste,
    app.get_current_tenant_id() as resultado,
    current_setting('app.current_tenant_id', true) as setting_atual;

-- Teste 2: Definir contexto e testar
SELECT app.set_current_tenant('1c11dd1a-97a8-45fd-a295-f56963f50f9a');
SELECT 
    'TESTE FUNÇÃO COM CONTEXTO' as teste,
    app.get_current_tenant_id() as resultado,
    current_setting('app.current_tenant_id', true) as setting_atual;

-- 10. TESTAR ACESSO COM RLS
-- =====================================================

-- Teste sem contexto
SELECT set_config('app.current_tenant_id', '', false);
SELECT 
    'ACESSO SEM CONTEXTO' as teste,
    COUNT(*) as total_users,
    array_agg(user_id) as user_ids
FROM app.users;

-- Teste com contexto
SELECT app.set_current_tenant('1c11dd1a-97a8-45fd-a295-f56963f50f9a');
SELECT 
    'ACESSO COM CONTEXTO' as teste,
    COUNT(*) as total_users,
    array_agg(user_id) as user_ids
FROM app.users;

-- 11. VERIFICAR PERMISSÕES DE ROLES
-- =====================================================
SELECT 
    'PERMISSÕES TABELA USERS' as secao,
    grantee,
    privilege_type,
    is_grantable
FROM information_schema.table_privileges 
WHERE table_schema = 'app' 
AND table_name = 'users'
ORDER BY grantee, privilege_type;

-- Verificar permissões das funções
SELECT 
    'PERMISSÕES FUNÇÕES' as secao,
    routine_name,
    routine_schema,
    security_type,
    definer_rights
FROM information_schema.routines 
WHERE routine_schema = 'app' 
AND routine_name IN ('set_current_tenant', 'get_current_tenant_id');

-- 12. SIMULAR PROBLEMA DO CLIENTE
-- =====================================================

-- Simular exatamente o que acontece no cliente
DO $$
DECLARE
    tenant_found UUID;
    context_result UUID;
    user_found RECORD;
BEGIN
    RAISE NOTICE '=== SIMULAÇÃO DO PROBLEMA DO CLIENTE ===';
    
    -- Passo 1: Buscar tenant pelo subdomínio
    SELECT id INTO tenant_found
    FROM app.tenants 
    WHERE subdomain = 'vasco' 
    AND is_active = true;
    
    RAISE NOTICE 'Tenant encontrado: %', tenant_found;
    
    -- Passo 2: Definir contexto
    IF tenant_found IS NOT NULL THEN
        PERFORM app.set_current_tenant(tenant_found);
        RAISE NOTICE 'Contexto definido para: %', tenant_found;
    END IF;
    
    -- Passo 3: Verificar contexto
    context_result := app.get_current_tenant_id();
    RAISE NOTICE 'Contexto verificado: %', context_result;
    RAISE NOTICE 'Setting atual: "%"', current_setting('app.current_tenant_id', true);
    
    -- Passo 4: Tentar buscar usuário (simula a query que falha)
    BEGIN
        SELECT user_id, tenant_id, full_name INTO user_found
        FROM app.users 
        WHERE user_id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d';
        
        IF user_found IS NOT NULL THEN
            RAISE NOTICE 'Usuário encontrado: % (tenant: %)', user_found.full_name, user_found.tenant_id;
        ELSE
            RAISE NOTICE 'Usuário NÃO encontrado';
        END IF;
        
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'ERRO ao buscar usuário: % (SQLSTATE: %)', SQLERRM, SQLSTATE;
    END;
    
    -- Passo 5: Testar com string vazia (simula problema HTTP)
    RAISE NOTICE '=== TESTANDO COM STRING VAZIA ===';
    PERFORM set_config('app.current_tenant_id', '', false);
    
    BEGIN
        context_result := app.get_current_tenant_id();
        RAISE NOTICE 'Contexto com string vazia: %', context_result;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'ERRO com string vazia: % (SQLSTATE: %)', SQLERRM, SQLSTATE;
    END;
    
END $$;

-- 13. VERIFICAR TRIGGERS
-- =====================================================
SELECT 
    'TRIGGERS' as secao,
    trigger_name,
    event_manipulation,
    action_timing,
    action_statement
FROM information_schema.triggers 
WHERE event_object_schema = 'auth'
AND event_object_table = 'users'
ORDER BY trigger_name;

-- 14. RESUMO FINAL
-- =====================================================
SELECT 
    'RESUMO DIAGNÓSTICO' as secao,
    'Verificar todos os resultados acima para identificar problemas' as instrucao;

-- =====================================================
-- INSTRUÇÕES:
-- 1. Execute este script completo no Supabase SQL Editor
-- 2. Analise todos os resultados
-- 3. Procure por erros ou valores inesperados
-- 4. Foque especialmente na seção "SIMULAÇÃO DO PROBLEMA"
-- =====================================================
