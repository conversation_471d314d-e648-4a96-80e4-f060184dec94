@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Forçar ECharts canvas a respeitar limites do container */
.echarts-container canvas {
  max-width: 100% !important;
  max-height: 100% !important;
  object-fit: contain !important;
}

/* Específico para mobile */
@media (max-width: 768px) {
  .echarts-container {
    overflow: hidden !important;
    max-width: 100% !important;
  }

  .echarts-container canvas {
    width: 100% !important;
    height: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;
    object-fit: contain !important;
    display: block !important;
  }
}

/* Estilos personalizados para o chat */
@layer components {
  .chat-container {
    height: calc(100vh - 4rem);
    display: flex;
    flex-direction: column;
  }

  .chat-message {
    max-width: 80%;
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
    line-height: 1.5;
  }

  /* Responsividade para mensagens do chat */
  @media (max-width: 768px) {
    .chat-message {
      max-width: 90%;
      padding: 0.5rem 0.75rem;
      font-size: 0.875rem;
    }
  }

  .chat-message-user {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    align-self: flex-end;
    border-bottom-right-radius: 0;
  }

  .chat-message-assistant {
    background-color: hsl(var(--muted));
    color: hsl(var(--foreground));
    align-self: flex-start;
    border-bottom-left-radius: 0;
  }

  .chat-avatar {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Responsividade para avatares */
  @media (max-width: 768px) {
    .chat-avatar {
      width: 1.5rem;
      height: 1.5rem;
    }
  }

  /* Estilos para melhorar a experiência mobile */
  @media (max-width: 768px) {
    /* Garantir que o viewport seja usado corretamente */
    html, body {
      overflow-x: hidden;
      /* Usar altura da viewport que considera barras do navegador */
      height: 100vh;
      height: 100dvh; /* Dynamic viewport height para navegadores modernos */
    }

    /* Melhorar o toque em elementos pequenos */
    button, [role="button"] {
      min-height: 44px;
      min-width: 44px;
    }

    /* Otimizar inputs para mobile */
    textarea, input {
      font-size: 16px; /* Previne zoom no iOS */
    }

    /* Garantir que elementos de altura total usem a viewport dinâmica */
    .h-screen {
      height: 100vh;
      height: 100dvh;
    }

    /* Garantir que drawers/modals mobile usem altura total */
    .mobile-drawer {
      height: 100vh !important;
      height: 100dvh !important;
    }

    /* Melhorar safe areas para dispositivos com notch */
    .mobile-safe-bottom {
      padding-bottom: max(1rem, env(safe-area-inset-bottom));
      margin-bottom: env(safe-area-inset-bottom, 0px);
    }

    .mobile-safe-top {
      padding-top: max(1rem, env(safe-area-inset-top));
    }
  }

    /* Adicionar padding bottom seguro para elementos que ficam no final da tela */
    .safe-bottom {
      padding-bottom: env(safe-area-inset-bottom, 20px);
    }

    /* Classe específica para Sheets no mobile */
    .pb-safe {
      padding-bottom: env(safe-area-inset-bottom, 80px) !important;
    }

    /* Estilos específicos para iOS Safari e Chrome mobile */
    @supports (-webkit-touch-callout: none) {
      .pb-safe {
        padding-bottom: 100px !important;
      }

      .safe-bottom {
        padding-bottom: 40px !important;
      }
    }

    /* Melhorar a experiência com barras de navegação que aparecem/desaparecem */
    .mobile-container {
      min-height: 100vh;
      min-height: 100dvh;
    }

    /* Estilos específicos para toasts no mobile */
    .mobile-toast {
      max-width: 90vw !important;
      margin: 0 auto !important;
      font-size: 14px !important;
    }

    /* Garantir que toasts fiquem acima de outros elementos no mobile */
    [data-sonner-toaster] {
      z-index: 9999 !important;
    }

    /* Ajustar posicionamento dos toasts no mobile */
    @media (max-width: 768px) {
      [data-sonner-toaster][data-position="top-center"] {
        top: env(safe-area-inset-top, 20px) !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        width: 100% !important;
        max-width: 100vw !important;
        padding: 0 16px !important;
      }
    }
  }
