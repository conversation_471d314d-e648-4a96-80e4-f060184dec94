"use client";

import { useState } from "react";
import { format } from "date-fns";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Database } from "@/types/database";
import { DatabaseList } from "./database-list";
import { DateFilter } from "./date-filter";

interface ExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  databases: Database[];
  selectedDatabases: string[];
  toggleDatabase: (id: string) => void;
  toggleSelectAll: () => void;
  allSelected: boolean;
  onExport: (dataReferencia?: string) => Promise<void>;
  isLoading: boolean;
}

export function ExportModal({
  isOpen,
  onClose,
  databases,
  selectedDatabases,
  toggleDatabase,
  toggleSelectAll,
  allSelected,
  onExport,
  isLoading,
}: ExportModalProps) {
  const [date, setDate] = useState<Date | undefined>(undefined);
  const [useDate, setUseDate] = useState(false);

  // Função para confirmar a exportação
  const confirmExport = async () => {
    const dataReferencia = useDate && date 
      ? format(date, "yyyy-MM-dd") 
      : undefined;
    
    await onExport(dataReferencia);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Exportar Dados</DialogTitle>
        </DialogHeader>
        
        <div className="py-4 space-y-6">
          {/* Filtro de Data */}
          <DateFilter 
            useDate={useDate}
            setUseDate={setUseDate}
            date={date}
            setDate={setDate}
          />
          
          {/* Lista de Bases de Dados */}
          <DatabaseList 
            databases={databases}
            selectedDatabases={selectedDatabases}
            toggleDatabase={toggleDatabase}
            toggleSelectAll={toggleSelectAll}
            allSelected={allSelected}
          />
        </div>
        
        <DialogFooter className="flex flex-row justify-between sm:justify-between gap-2">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancelar
          </Button>
          <Button 
            onClick={confirmExport} 
            disabled={selectedDatabases.length === 0 || (useDate && !date) || isLoading}
            className="bg-primary"
          >
            {isLoading ? "Exportando..." : "Exportar"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 