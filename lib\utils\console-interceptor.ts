/**
 * Interceptador de console para suprimir logs que expõem URLs do Supabase
 * e outras informações sensíveis
 */

type ConsoleMethod = 'log' | 'error' | 'warn' | 'info' | 'debug';

interface InterceptorConfig {
  suppressSupabaseUrls: boolean;
  suppressNetworkErrors: boolean;
  allowedInDevelopment: boolean;
  customPatterns: string[];
}

class ConsoleInterceptor {
  private originalMethods: Record<ConsoleMethod, (...args: any[]) => void> = {} as any;
  private isActive = false;
  private config: InterceptorConfig;

  constructor(config: Partial<InterceptorConfig> = {}) {
    this.config = {
      suppressSupabaseUrls: true,
      suppressNetworkErrors: true,
      allowedInDevelopment: false,
      customPatterns: [],
      ...config
    };

    // Salvar métodos originais
    this.originalMethods.log = console.log;
    this.originalMethods.error = console.error;
    this.originalMethods.warn = console.warn;
    this.originalMethods.info = console.info;
    this.originalMethods.debug = console.debug;
  }

  /**
   * Verifica se uma mensagem deve ser suprimida
   */
  private shouldSuppress(args: any[]): boolean {
    const isDevelopment = process.env.NODE_ENV === 'development';

    // Em desenvolvimento, permitir logs se configurado
    if (isDevelopment && this.config.allowedInDevelopment) {
      return false;
    }

    // Converter argumentos para string para análise
    const message = args.map(arg => {
      if (typeof arg === 'string') return arg;
      if (typeof arg === 'object') return JSON.stringify(arg);
      return String(arg);
    }).join(' ');

    // Verificar URLs do Supabase
    if (this.config.suppressSupabaseUrls) {
      const supabasePatterns = [
        /https?:\/\/[a-zA-Z0-9-]+\.supabase\.co/,
        /https?:\/\/[a-zA-Z0-9-]+\.supabase\.io/,
        /\.supabase\./,
        /supabase\.co/,
        /supabase\.io/,
        /auth\/v1\/token/,
        /rest\/v1\//
      ];

      if (supabasePatterns.some(pattern => pattern.test(message))) {
        return true;
      }
    }

    // Verificar erros de rede
    if (this.config.suppressNetworkErrors) {
      const networkPatterns = [
        /POST.*400.*Bad Request/,
        /GET.*401.*Unauthorized/,
        /POST.*401.*Unauthorized/,
        /Failed to fetch/,
        /Network request failed/,
        /ERR_NETWORK/,
        /ERR_INTERNET_DISCONNECTED/
      ];

      if (networkPatterns.some(pattern => pattern.test(message))) {
        return true;
      }
    }

    // Verificar padrões customizados
    if (this.config.customPatterns.length > 0) {
      const customRegexes = this.config.customPatterns.map(pattern => new RegExp(pattern, 'i'));
      if (customRegexes.some(regex => regex.test(message))) {
        return true;
      }
    }

    return false;
  }

  /**
   * Cria um método interceptado
   */
  private createInterceptedMethod(method: ConsoleMethod): (...args: any[]) => void {
    return (...args: any[]) => {
      if (this.shouldSuppress(args)) {
        // Suprimir o log
        return;
      }

      // Permitir o log original
      (this.originalMethods[method] as any).apply(console, args);
    };
  }

  /**
   * Ativa a interceptação
   */
  activate(): void {
    if (this.isActive) return;

    console.log = this.createInterceptedMethod('log');
    console.error = this.createInterceptedMethod('error');
    console.warn = this.createInterceptedMethod('warn');
    console.info = this.createInterceptedMethod('info');
    console.debug = this.createInterceptedMethod('debug');

    this.isActive = true;
  }

  /**
   * Desativa a interceptação
   */
  deactivate(): void {
    if (!this.isActive) return;

    console.log = this.originalMethods.log;
    console.error = this.originalMethods.error;
    console.warn = this.originalMethods.warn;
    console.info = this.originalMethods.info;
    console.debug = this.originalMethods.debug;

    this.isActive = false;
  }

  /**
   * Executa uma função com interceptação temporária
   */
  async withInterception<T>(fn: () => Promise<T>): Promise<T> {
    this.activate();
    try {
      return await fn();
    } finally {
      this.deactivate();
    }
  }

  /**
   * Executa uma função síncrona com interceptação temporária
   */
  withInterceptionSync<T>(fn: () => T): T {
    this.activate();
    try {
      return fn();
    } finally {
      this.deactivate();
    }
  }

  /**
   * Atualiza a configuração
   */
  updateConfig(newConfig: Partial<InterceptorConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Verifica se está ativo
   */
  isIntercepting(): boolean {
    return this.isActive;
  }

  /**
   * Log seguro que sempre funciona (bypass da interceptação)
   */
  safeLog(message: string, ...args: any[]): void {
    (this.originalMethods.log as any)(message, ...args);
  }

  /**
   * Error seguro que sempre funciona (bypass da interceptação)
   */
  safeError(message: string, ...args: any[]): void {
    (this.originalMethods.error as any)(message, ...args);
  }
}

// Instância global
const globalInterceptor = new ConsoleInterceptor({
  suppressSupabaseUrls: true,
  suppressNetworkErrors: true,
  allowedInDevelopment: false, // Mudar para true se quiser ver logs em dev
  customPatterns: [
    'Invalid login credentials',
    'User already registered',
    'Email not confirmed',
    'Token has expired'
  ]
});

// Função para interceptação temporária de operações do Supabase
export const withSupabaseInterception = async <T>(fn: () => Promise<T>): Promise<T> => {
  return globalInterceptor.withInterception(fn);
};

// Função para interceptação temporária síncrona
export const withSupabaseInterceptionSync = <T>(fn: () => T): T => {
  return globalInterceptor.withInterceptionSync(fn);
};

// Ativar interceptação global (opcional)
export const activateGlobalInterception = () => {
  globalInterceptor.activate();
};

// Desativar interceptação global
export const deactivateGlobalInterception = () => {
  globalInterceptor.deactivate();
};

// Exports
export { ConsoleInterceptor, globalInterceptor };
export default globalInterceptor;
