'use client';

import { useState, useEffect, useCallback } from 'react';
import { createClient } from '@/utils/supabase/client';

interface UseCreditsReturn {
  credits: number;
  loading: boolean;
  updateCredits: (newCredits: number) => void;
  refreshCredits: () => Promise<void>;
  decrementCredits: () => void;
}

export function useCredits(userId: string): UseCreditsReturn {
  const [credits, setCredits] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const supabase = createClient();

  const fetchCredits = useCallback(async () => {
    if (!userId) return;

    try {
      const response = await fetch(`/api/credits?user_id=${userId}`);
      const data = await response.json();
      
      if (response.ok) {
        setCredits(data.credits);
      } else {
        console.error('Erro ao buscar créditos:', data.error);
      }
    } catch (error) {
      console.error('Erro ao buscar créditos:', error);
    } finally {
      setLoading(false);
    }
  }, [userId]);

  const updateCredits = useCallback((newCredits: number) => {
    setCredits(newCredits);
  }, []);

  const refreshCredits = useCallback(async () => {
    await fetchCredits();
  }, [fetchCredits]);

  const decrementCredits = useCallback(() => {
    setCredits(prev => Math.max(0, prev - 1));
  }, []);

  // Buscar créditos iniciais
  useEffect(() => {
    if (userId) {
      fetchCredits();
    }
  }, [userId, fetchCredits]);

  // Escutar mudanças em tempo real
  useEffect(() => {
    if (!userId) return;

    const channel = supabase
      .channel('credits-changes')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'app',
          table: 'users',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          console.log('🔄 Créditos atualizados via Realtime:', payload);
          if (payload.new && typeof payload.new.credits === 'number') {
            setCredits(payload.new.credits);
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [userId, supabase]);

  return {
    credits,
    loading,
    updateCredits,
    refreshCredits,
    decrementCredits
  };
}
