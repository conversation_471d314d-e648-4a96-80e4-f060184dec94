'use client';

import { useMemo } from 'react';
import { Area, AreaChart, CartesianGrid, XAxis, YAxis, ResponsiveContainer } from 'recharts';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';

interface TokensOnlyChartProps {
  data: Array<{
    date: string;
    total_tokens: number;
    total_messages: number;
  }>;
  groupBy: string;
}

const chartConfig = {
  total_tokens: {
    label: 'Tokens',
    color: 'hsl(var(--chart-1))',
  },
} satisfies ChartConfig;

export default function TokensOnlyChart({ data, groupBy }: TokensOnlyChartProps) {
  const chartData = useMemo(() => {
    return data.map(item => {
      // A data já vem processada da API no formato YYYY-MM-DD
      const date = new Date(item.date + 'T12:00:00');

      let formattedDate: string;

      switch (groupBy) {
        case 'week':
          formattedDate = `${date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' })}`;
          break;
        case 'month':
          formattedDate = date.toLocaleDateString('pt-BR', { month: 'short', year: 'numeric' });
          break;
        default:
          formattedDate = date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
      }

      return {
        date: formattedDate,
        total_tokens: item.total_tokens,
      };
    });
  }, [data, groupBy]);

  const formatTokens = (value: number) => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toString();
  };

  if (!chartData.length) {
    return (
      <div className="flex items-center justify-center h-[400px] text-muted-foreground">
        Nenhum dado disponível para o período selecionado
      </div>
    );
  }

  return (
    <ChartContainer config={chartConfig} className="h-[400px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
          <XAxis 
            dataKey="date" 
            className="text-xs"
            angle={chartData.length > 7 ? -45 : 0}
            textAnchor={chartData.length > 7 ? 'end' : 'middle'}
            height={chartData.length > 7 ? 60 : 30}
          />
          <YAxis 
            className="text-xs"
            tickFormatter={formatTokens}
          />
          <ChartTooltip 
            content={
              <ChartTooltipContent 
                formatter={(value) => [formatTokens(Number(value)), 'Tokens']}
              />
            }
          />
          <Area
            type="monotone"
            dataKey="total_tokens"
            stroke="hsl(var(--chart-1))"
            fill="hsl(var(--chart-1))"
            fillOpacity={0.2}
            strokeWidth={2}
          />
        </AreaChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
}
