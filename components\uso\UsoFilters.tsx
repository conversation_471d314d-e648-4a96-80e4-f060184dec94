'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar, Filter, RefreshCw } from 'lucide-react';

interface UsoFiltersProps {
  onFiltersChange: (filters: {
    startDate: string;
    endDate: string;
    groupBy: string;
  }) => void;
  loading: boolean;
  initialFilters?: {
    startDate: string;
    endDate: string;
    groupBy: string;
  };
}

export default function UsoFilters({ onFiltersChange, loading, initialFilters }: UsoFiltersProps) {
  const [startDate, setStartDate] = useState(() => {
    if (initialFilters?.startDate) return initialFilters.startDate;
    const date = new Date();
    const start = new Date(date.getFullYear(), date.getMonth(), 1); // Primeiro dia do mês atual
    return start.toISOString().split('T')[0];
  });
  
  const [endDate, setEndDate] = useState(() => {
    if (initialFilters?.endDate) return initialFilters.endDate;
    const date = new Date();
    const end = new Date(date.getFullYear(), date.getMonth() + 1, 0); // Último dia do mês atual
    return end.toISOString().split('T')[0];
  });
  
  const [groupBy, setGroupBy] = useState(initialFilters?.groupBy || 'day');

  // Sincronizar com filtros iniciais quando mudarem
  useEffect(() => {
    if (initialFilters) {
      setStartDate(initialFilters.startDate);
      setEndDate(initialFilters.endDate);
      setGroupBy(initialFilters.groupBy);
    }
  }, [initialFilters]);

  // Aplicar filtros automaticamente quando o componente carregar
  useEffect(() => {
    // Só aplicar se não temos filtros iniciais (primeira carga)
    if (!initialFilters) {
      onFiltersChange({
        startDate,
        endDate,
        groupBy
      });
    }
  }, []); // Executar apenas uma vez na montagem



  // Função para verificar se um filtro rápido está ativo
  const isQuickFilterActive = (type: 'last7' | 'last30' | 'currentMonth' | 'lastMonth') => {
    const now = new Date();

    switch (type) {
      case 'last7':
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(now.getDate() - 7);
        const expectedStart7 = sevenDaysAgo.toISOString().split('T')[0];
        const expectedEnd7 = now.toISOString().split('T')[0];
        return startDate === expectedStart7 && endDate === expectedEnd7;

      case 'last30':
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(now.getDate() - 30);
        const expectedStart30 = thirtyDaysAgo.toISOString().split('T')[0];
        const expectedEnd30 = now.toISOString().split('T')[0];
        return startDate === expectedStart30 && endDate === expectedEnd30;

      case 'currentMonth':
        const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
        const currentMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        const expectedStartCurrent = currentMonthStart.toISOString().split('T')[0];
        const expectedEndCurrent = currentMonthEnd.toISOString().split('T')[0];

        return startDate === expectedStartCurrent && endDate === expectedEndCurrent;

      case 'lastMonth':
        const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
        const expectedStartLast = lastMonthStart.toISOString().split('T')[0];
        const expectedEndLast = lastMonthEnd.toISOString().split('T')[0];
        return startDate === expectedStartLast && endDate === expectedEndLast;

      default:
        return false;
    }
  };

  const handleApplyFilters = () => {
    onFiltersChange({
      startDate,
      endDate,
      groupBy
    });
  };

  const handleQuickFilter = (days: number) => {
    const end = new Date();
    const start = new Date();
    start.setDate(end.getDate() - days);
    
    setStartDate(start.toISOString().split('T')[0]);
    setEndDate(end.toISOString().split('T')[0]);
    
    onFiltersChange({
      startDate: start.toISOString().split('T')[0],
      endDate: end.toISOString().split('T')[0],
      groupBy
    });
  };

  const handleCurrentMonth = () => {
    const now = new Date();
    const start = new Date(now.getFullYear(), now.getMonth(), 1);
    const end = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    
    setStartDate(start.toISOString().split('T')[0]);
    setEndDate(end.toISOString().split('T')[0]);
    
    onFiltersChange({
      startDate: start.toISOString().split('T')[0],
      endDate: end.toISOString().split('T')[0],
      groupBy
    });
  };

  const handleLastMonth = () => {
    const now = new Date();
    const start = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const end = new Date(now.getFullYear(), now.getMonth(), 0);
    
    setStartDate(start.toISOString().split('T')[0]);
    setEndDate(end.toISOString().split('T')[0]);
    
    onFiltersChange({
      startDate: start.toISOString().split('T')[0],
      endDate: end.toISOString().split('T')[0],
      groupBy
    });
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Filter className="h-5 w-5" />
          Filtros de Período
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
          <div className="space-y-2">
            <Label htmlFor="start-date">Data Inicial</Label>
            <div className="relative">
              <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="start-date"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="end-date">Data Final</Label>
            <div className="relative">
              <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="end-date"
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="group-by">Agrupar por</Label>
            <Select value={groupBy} onValueChange={setGroupBy}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione o agrupamento" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="day">Dia</SelectItem>
                <SelectItem value="week">Semana</SelectItem>
                <SelectItem value="month">Mês</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Aplicar Filtros</Label>
            <Button 
              onClick={handleApplyFilters} 
              className="w-full"
              disabled={loading}
            >
              {loading ? (
                <RefreshCw className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Filter className="h-4 w-4 mr-2" />
              )}
              Aplicar
            </Button>
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button
            variant={isQuickFilterActive('last7') ? "default" : "outline"}
            size="sm"
            onClick={() => handleQuickFilter(7)}
            disabled={loading}
          >
            Últimos 7 dias
          </Button>
          <Button
            variant={isQuickFilterActive('last30') ? "default" : "outline"}
            size="sm"
            onClick={() => handleQuickFilter(30)}
            disabled={loading}
          >
            Últimos 30 dias
          </Button>
          <Button
            variant={isQuickFilterActive('currentMonth') ? "default" : "outline"}
            size="sm"
            onClick={handleCurrentMonth}
            disabled={loading}
          >
            Mês atual
          </Button>
          <Button
            variant={isQuickFilterActive('lastMonth') ? "default" : "outline"}
            size="sm"
            onClick={handleLastMonth}
            disabled={loading}
          >
            Mês passado
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
