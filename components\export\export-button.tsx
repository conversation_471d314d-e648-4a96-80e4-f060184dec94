"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Download, RefreshCw } from "lucide-react";
import { ExportModal } from "./export-modal";
import { useExport } from "./use-export";
import { Toaster } from "sonner";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { createClient } from "@/utils/supabase/client";

interface ExportButtonProps {
  className?: string;
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
}

export function ExportButton({ 
  className = "",
  variant = "default"
}: ExportButtonProps) {
  const router = useRouter();
  const [isCheckingAuth, setIsCheckingAuth] = useState(false);
  const [authStatus, setAuthStatus] = useState<'unknown' | 'checking' | 'authenticated' | 'unauthenticated'>('unknown');
  
  const { 
    isOpen, 
    setIsOpen, 
    databases, 
    selectedDatabases, 
    toggleDatabase,
    toggleSelectAll,
    allSelected,
    exportData,
    isExporting,
    accessToken,
    refreshAccessToken
  } = useExport();
  
  // Verificar estado de autenticação quando o componente montar
  useEffect(() => {
    const checkAuth = async () => {
      setAuthStatus('checking');
      try {
        await refreshAccessToken();
        setAuthStatus(accessToken ? 'authenticated' : 'unauthenticated');
      } catch (error) {
        console.error("[ExportButton] Erro ao verificar autenticação:", error);
        setAuthStatus('unauthenticated');
      }
    };
    
    checkAuth();
  }, [refreshAccessToken, accessToken]);
  
  // Verificar autenticação antes de abrir o modal
  const handleExportClick = async () => {
    try {
      setIsCheckingAuth(true);
      setAuthStatus('checking');
      
      await refreshAccessToken();
      
      if (!accessToken) {
        console.error("[ExportButton] Token de acesso não disponível");
        toast.error("Você precisa estar autenticado para exportar dados.");
        setAuthStatus('unauthenticated');
        router.push("/login");
        return;
      }
      
      setAuthStatus('authenticated');
      // Usuário está autenticado, prosseguir com a exportação
      setIsOpen(true);
    } catch (error) {
      console.error("[ExportButton] Erro ao verificar autenticação:", error);
      toast.error("Não foi possível verificar sua autenticação.");
      setAuthStatus('unauthenticated');
    } finally {
      setIsCheckingAuth(false);
    }
  };
  
  // Função para mostrar o texto do botão conforme o estado
  const getButtonText = () => {
    if (isCheckingAuth || authStatus === 'checking') return "Verificando...";
    if (authStatus === 'unauthenticated') return "Faça login para exportar";
    return "Exportar Dados";
  };

  return (
    <>
      <Toaster richColors position="top-right" />
      
      <Button
        onClick={handleExportClick}
        className={`flex items-center gap-2 ${className}`}
        variant={variant}
        disabled={isCheckingAuth || isExporting || authStatus === 'checking'}
      >
        {authStatus === 'checking' ? (
          <RefreshCw size={16} className="animate-spin" />
        ) : (
          <Download size={16} />
        )}
        {getButtonText()}
      </Button>
      
      <ExportModal
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        databases={databases}
        selectedDatabases={selectedDatabases}
        toggleDatabase={toggleDatabase}
        toggleSelectAll={toggleSelectAll}
        allSelected={allSelected}
        onExport={exportData}
        isLoading={isExporting}
      />
    </>
  );
} 