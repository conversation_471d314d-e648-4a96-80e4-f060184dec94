'use client';

import { useTenant } from '@/lib/tenant-context';
import Image from 'next/image';

interface TenantLogoProps {
  className?: string;
  showName?: boolean;
}

export function TenantLogo({ className = '', showName = true }: TenantLogoProps) {
  const { tenant, isLoading } = useTenant();

  if (isLoading) {
    return (
      <div className={`${className} animate-pulse bg-gray-200 rounded h-8 w-32`} />
    );
  }

  // Se não há tenant (domínio principal), mostrar logo padrão
  if (!tenant) {
    return (
      <div className={className}>
        <h1 className="text-xl font-bold text-gray-900">
          PrimeAI
        </h1>
      </div>
    );
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {tenant.logoUrl ? (
        <Image
          src={tenant.logoUrl}
          alt={`${tenant.name} Logo`}
          width={120}
          height={40}
          className="object-contain max-h-10"
        />
      ) : (
        showName && (
          <h1 
            className="text-xl font-bold" 
            style={{ color: tenant.primaryColor }}
          >
            {tenant.name}
          </h1>
        )
      )}
    </div>
  );
}
