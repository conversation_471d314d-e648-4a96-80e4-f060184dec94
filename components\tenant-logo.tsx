'use client';

import { useTenant } from '@/lib/tenant-context';
import Image from 'next/image';

interface TenantLogoProps {
  className?: string;
  showName?: boolean;
}

export function TenantLogo({ className = '', showName = true }: TenantLogoProps) {
  const { tenant, isLoading } = useTenant();

  if (isLoading) {
    return (
      <div className={`${className} animate-pulse bg-gray-200 rounded h-8 w-32`} />
    );
  }

  // Se não há tenant (domínio principal), mostrar logo padrão
  if (!tenant) {
    return (
      <div className={`flex items-center gap-3 ${className}`}>
        <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-bold">
          P
        </div>
        {showName && <span className="font-semibold">PrimeAI</span>}
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      {tenant.logoUrl ? (
        <Image
          src={tenant.logoUrl}
          alt={`${tenant.name} Logo`}
          width={120}
          height={40}
          className="object-contain max-h-10"
        />
      ) : (
        <>
          <div
            className="w-8 h-8 rounded-full flex items-center justify-center text-white font-bold"
            style={{ backgroundColor: tenant.primaryColor }}
          >
            {tenant.name.charAt(0).toUpperCase()}
          </div>
          {showName && (
            <span
              className="font-semibold"
              style={{ color: tenant.primaryColor }}
            >
              {tenant.name}
            </span>
          )}
        </>
      )}
    </div>
  );
}
