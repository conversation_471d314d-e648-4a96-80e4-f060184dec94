"use client";

import { useState } from "react";
import Link from "next/link";
import { toast } from "sonner";
import { forgotPasswordClient } from "@/app/client-actions";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { FormMessage, Message } from "@/components/form-message";

interface ForgotPasswordFormClientProps {
  initialMessage?: Message;
}

export default function ForgotPasswordFormClient({ initialMessage }: ForgotPasswordFormClientProps) {
  const [message, setMessage] = useState<Message | null>(initialMessage || null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsSubmitting(true);

    try {
      const formData = new FormData(event.currentTarget);
      const result = await forgotPasswordClient(formData);

      if (result.success) {
        const successMessage = result.message || "Email de recuperação enviado com sucesso!";

        // Toast de sucesso
        toast.success("Email enviado!", {
          description: successMessage,
          duration: 5000,
        });

        setMessage({
          success: successMessage
        });
      }
    } catch (error: any) {
      const errorMessage = error.message || "Falha ao enviar email de recuperação";

      // Definir mensagem de erro mais específica
      let userFriendlyMessage = errorMessage;
      if (errorMessage.includes("Invalid email")) {
        userFriendlyMessage = "Email inválido";
      } else if (errorMessage.includes("User not found")) {
        userFriendlyMessage = "Email não encontrado";
      } else if (errorMessage.includes("Too many requests")) {
        userFriendlyMessage = "Muitas tentativas. Tente novamente em alguns minutos";
      }

      // Toast de erro
      toast.error("Erro ao enviar email", {
        description: userFriendlyMessage,
        duration: 4000,
        action: {
          label: "Tentar novamente",
          onClick: () => {
            setMessage(null);
          }
        }
      });

      setMessage({
        error: userFriendlyMessage
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form className="w-full flex flex-col" onSubmit={handleSubmit}>
      <h1 className="text-2xl font-medium text-center mb-2">Redefinir senha</h1>
      <p className="text-sm text-foreground text-center mb-8">
        Já tem uma conta?{" "}
        <Link className="text-primary underline" href="/sign-in">
          Entrar
        </Link>
      </p>
      <div className="flex flex-col gap-4">
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input name="email" placeholder="<EMAIL>" required />
        </div>
        <div className="pt-4">
          <SubmitButton
            className="w-full"
            pendingText="Enviando link..."
            disabled={isSubmitting}
          >
            Redefinir senha
          </SubmitButton>
        </div>
        {message && <FormMessage message={message} />}
      </div>
    </form>
  );
}