"use client";

import { useEffect } from "react";
import { activateGlobalInterception } from "@/lib/utils/console-interceptor";

interface ConsoleSecurityProviderProps {
  children: React.ReactNode;
}

/**
 * Provider que ativa a interceptação global de logs do console
 * para suprimir informações sensíveis do Supabase
 */
export function ConsoleSecurityProvider({ children }: ConsoleSecurityProviderProps) {
  useEffect(() => {
    // Ativar interceptação global apenas em produção
    if (process.env.NODE_ENV === 'production') {
      activateGlobalInterception();
      
      // Log seguro para confirmar ativação
      console.info('🛡️ Interceptação de segurança do console ativada');
    } else {
      // Em desenvolvimento, apenas avisar
      console.info('🔧 Modo desenvolvimento - Logs do Supabase podem ser visíveis');
    }

    // Cleanup não é necessário pois queremos manter a interceptação ativa
  }, []);

  return <>{children}</>;
}
