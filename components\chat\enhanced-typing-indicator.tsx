"use client";

import { forwardRef, useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Bo<PERSON>, Clock } from "lucide-react";
import { AppConfig } from "@/lib/config/app-config";

interface EnhancedTypingIndicatorProps {
  className?: string;
  onComplete?: () => void;
}

const EnhancedTypingIndicator = forwardRef<HTMLDivElement, EnhancedTypingIndicatorProps>(
  ({ className = "", onComplete }, ref) => {
    const [timeElapsed, setTimeElapsed] = useState(0);
    const [currentMessage, setCurrentMessage] = useState("");

    const { duration, enableCountdown, progressMessages } = AppConfig.typingIndicator;

    // Atualizar tempo e mensagem
    useEffect(() => {
      if (!enableCountdown) {
        setCurrentMessage("Digitando");
        return;
      }

      const interval = setInterval(() => {
        setTimeElapsed(prev => {
          const newTime = prev + 1;
          
          // Encontrar mensagem apropriada para o tempo atual
          const currentMessageObj = progressMessages.find(
            msg => newTime >= msg.timeRange[0] && newTime < msg.timeRange[1]
          );
          
          if (currentMessageObj) {
            setCurrentMessage(currentMessageObj.message);
          }

          // Se passou do tempo configurado, chamar callback se existir
          if (newTime >= duration && onComplete) {
            onComplete();
          }

          return newTime;
        });
      }, 1000);

      return () => clearInterval(interval);
    }, [duration, enableCountdown, progressMessages, onComplete]);

    // Calcular progresso (0 a 100)
    const progress = enableCountdown ? Math.min((timeElapsed / duration) * 100, 100) : 0;
    const remainingTime = Math.max(duration - timeElapsed, 0);

    return (
      <AnimatePresence>
        <motion.div
            ref={ref}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3 }}
            className={`flex gap-3 justify-start ${className}`}
          >
            <div className="flex-shrink-0">
              <div className="h-8 w-8 bg-primary/10 rounded-full flex items-center justify-center relative">
                {/* Ícone do robô - só aparece quando countdown está desabilitado ou quando não há tempo restante */}
                <AnimatePresence>
                  {(!enableCountdown || remainingTime === 0) && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Bot className="h-5 w-5 text-primary" />
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Círculo de progresso - só aparece durante o countdown */}
                {enableCountdown && remainingTime > 0 && (
                  <motion.div
                    className="absolute inset-0 rounded-full border-2 border-primary/30"
                    style={{
                      background: `conic-gradient(from 0deg, rgb(var(--primary)) ${progress}%, transparent ${progress}%)`
                    }}
                    initial={{ rotate: 0, opacity: 0 }}
                    animate={{ rotate: 360, opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{
                      rotate: { duration: 2, repeat: Infinity, ease: "linear" },
                      opacity: { duration: 0.3 }
                    }}
                  />
                )}

                {/* Ícone de relógio no centro durante o countdown */}
                {enableCountdown && remainingTime > 0 && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    transition={{ duration: 0.3 }}
                    className="z-10"
                  >
                    <Clock className="h-4 w-4 text-primary" />
                  </motion.div>
                )}
              </div>
            </div>

            <div className="flex-1 max-w-[80%] rounded-lg p-4 bg-muted">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {/* Animação de pontos */}
                  <div className="flex space-x-1">
                    {[0, 1, 2].map((index) => (
                      <motion.div
                        key={index}
                        className="w-2 h-2 bg-gray-500 rounded-full"
                        animate={{
                          scale: [1, 1.2, 1],
                          opacity: [0.5, 1, 0.5]
                        }}
                        transition={{
                          duration: 1.4,
                          repeat: Infinity,
                          delay: index * 0.2
                        }}
                      />
                    ))}
                  </div>

                  {/* Mensagem dinâmica */}
                  <AnimatePresence mode="wait">
                    <motion.span
                      key={currentMessage}
                      initial={{ opacity: 0, x: 10 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -10 }}
                      transition={{ duration: 0.3 }}
                      className="text-sm text-muted-foreground"
                    >
                      {currentMessage}
                    </motion.span>
                  </AnimatePresence>
                </div>

                {/* Contador de tempo (opcional) */}
                {enableCountdown && remainingTime > 0 && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="flex items-center space-x-1 text-xs text-muted-foreground/70"
                  >
                    <Clock className="h-3 w-3" />
                    <span>{remainingTime}s</span>
                  </motion.div>
                )}
              </div>

              {/* Barra de progresso */}
              {enableCountdown && (
                <motion.div
                  className="mt-2 h-1 bg-gray-200 rounded-full overflow-hidden"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  <motion.div
                    className="h-full bg-primary rounded-full"
                    initial={{ width: "0%" }}
                    animate={{ width: `${progress}%` }}
                    transition={{ duration: 0.5, ease: "easeOut" }}
                  />
                </motion.div>
              )}
            </div>
          </motion.div>
      </AnimatePresence>
    );
  }
);

EnhancedTypingIndicator.displayName = "EnhancedTypingIndicator";

export default EnhancedTypingIndicator;
