'use client';

import { useTenant } from '@/lib/tenant-context';
import { useEffect } from 'react';

export function TenantTheme() {
  const { tenant } = useTenant();

  useEffect(() => {
    if (tenant) {
      // Aplicar cores personalizadas via CSS variables
      const root = document.documentElement;
      
      // Converter hex para HSL para melhor controle
      const hexToHsl = (hex: string) => {
        const r = parseInt(hex.slice(1, 3), 16) / 255;
        const g = parseInt(hex.slice(3, 5), 16) / 255;
        const b = parseInt(hex.slice(5, 7), 16) / 255;

        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        let h = 0, s = 0, l = (max + min) / 2;

        if (max !== min) {
          const d = max - min;
          s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
          switch (max) {
            case r: h = (g - b) / d + (g < b ? 6 : 0); break;
            case g: h = (b - r) / d + 2; break;
            case b: h = (r - g) / d + 4; break;
          }
          h /= 6;
        }

        return [Math.round(h * 360), Math.round(s * 100), Math.round(l * 100)];
      };

      try {
        const [h, s, l] = hexToHsl(tenant.primaryColor);
        
        // Aplicar cor primária do tenant
        root.style.setProperty('--primary', `${h} ${s}% ${l}%`);
        root.style.setProperty('--primary-foreground', l > 50 ? '0 0% 0%' : '0 0% 100%');
        
        // Aplicar cor secundária se disponível
        if (tenant.secondaryColor && tenant.secondaryColor !== '#ffffff') {
          const [h2, s2, l2] = hexToHsl(tenant.secondaryColor);
          root.style.setProperty('--secondary', `${h2} ${s2}% ${l2}%`);
        }
        
        // Aplicar CSS customizado se disponível
        if (tenant.customCss) {
          let styleElement = document.getElementById('tenant-custom-styles');
          if (!styleElement) {
            styleElement = document.createElement('style');
            styleElement.id = 'tenant-custom-styles';
            document.head.appendChild(styleElement);
          }
          styleElement.textContent = tenant.customCss;
        }

        // Atualizar título da página
        document.title = `${tenant.name} - PrimeAI`;
        
      } catch (error) {
        console.error('Erro ao aplicar tema do tenant:', error);
      }
    } else {
      // Resetar para tema padrão
      const root = document.documentElement;
      root.style.removeProperty('--primary');
      root.style.removeProperty('--primary-foreground');
      root.style.removeProperty('--secondary');
      
      // Remover CSS customizado
      const styleElement = document.getElementById('tenant-custom-styles');
      if (styleElement) {
        styleElement.remove();
      }
      
      // Resetar título
      document.title = 'PrimeAI - Assistente Financeiro';
    }
  }, [tenant]);

  return null; // Este componente não renderiza nada visualmente
}
