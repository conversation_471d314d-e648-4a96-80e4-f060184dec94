-- =====================================================
-- DIAGNÓSTICO COMPLETO DO RLS
-- Execute no Supabase SQL Editor
-- =====================================================

-- 1. VERIFICAR SE FUNÇÕES EXISTEM NO SCHEMA APP
-- =====================================================
SELECT 
    'FUNÇÕES RLS' as check_type,
    routine_name,
    routine_schema,
    routine_type,
    security_type
FROM information_schema.routines 
WHERE routine_schema = 'app' 
AND routine_name IN ('set_current_tenant', 'get_current_tenant_id')
ORDER BY routine_name;

-- 2. VERIFICAR POLÍTICAS RLS DA TABELA USERS
-- =====================================================
SELECT 
    'POLÍTICAS RLS' as check_type,
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual as condition
FROM pg_policies 
WHERE schemaname = 'app'
AND tablename = 'users'
ORDER BY policyname;

-- 3. VERIFICAR SE RLS ESTÁ HABILITADO
-- =====================================================
SELECT 
    'RLS STATUS' as check_type,
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'app' 
AND tablename = 'users';

-- 4. TESTAR FUNÇÕES RLS MANUALMENTE
-- =====================================================

-- Teste 1: Limpar contexto
SELECT set_config('app.current_tenant_id', '', false) as clear_context;

-- Teste 2: Verificar contexto vazio
SELECT 
    'CONTEXTO VAZIO' as teste,
    app.get_current_tenant_id() as current_tenant,
    current_setting('app.current_tenant_id', true) as raw_setting;

-- Teste 3: Definir contexto
SELECT app.set_current_tenant('1c11dd1a-97a8-45fd-a295-f56963f50f9a') as set_result;

-- Teste 4: Verificar contexto definido
SELECT 
    'CONTEXTO DEFINIDO' as teste,
    app.get_current_tenant_id() as current_tenant,
    current_setting('app.current_tenant_id', true) as raw_setting;

-- 5. TESTAR ACESSO À TABELA USERS COM DIFERENTES CONTEXTOS
-- =====================================================

-- Teste 5.1: Sem contexto (deve retornar vazio)
SELECT set_config('app.current_tenant_id', '', false);
SELECT 
    'SEM CONTEXTO' as teste,
    COUNT(*) as user_count,
    array_agg(user_id) as user_ids
FROM app.users;

-- Teste 5.2: Com contexto Vasco (deve retornar usuários)
SELECT app.set_current_tenant('1c11dd1a-97a8-45fd-a295-f56963f50f9a');
SELECT 
    'COM CONTEXTO VASCO' as teste,
    COUNT(*) as user_count,
    array_agg(user_id) as user_ids
FROM app.users;

-- Teste 5.3: Buscar usuário específico
SELECT 
    'USUÁRIO ESPECÍFICO' as teste,
    user_id,
    full_name,
    email,
    tenant_id,
    credits
FROM app.users 
WHERE user_id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d';

-- 6. VERIFICAR DADOS DO USUÁRIO E TENANT
-- =====================================================

-- Verificar se usuário existe (sem RLS)
SET ROLE postgres;
SELECT 
    'DADOS DO USUÁRIO (BYPASS RLS)' as info,
    user_id,
    full_name,
    email,
    tenant_id,
    credits
FROM app.users 
WHERE user_id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d';

-- Verificar tenant Vasco
SELECT 
    'TENANT VASCO' as info,
    id,
    subdomain,
    name,
    is_active
FROM app.tenants 
WHERE subdomain = 'vasco';

-- Voltar para role padrão
RESET ROLE;

-- 7. SIMULAR PROBLEMA DO CLIENTE
-- =====================================================

-- Simular sequência do cliente
DO $$
DECLARE
    tenant_found UUID;
    context_set BOOLEAN := false;
    user_found RECORD;
BEGIN
    -- Passo 1: Buscar tenant pelo subdomínio
    SELECT id INTO tenant_found
    FROM app.tenants 
    WHERE subdomain = 'vasco' 
    AND is_active = true;
    
    RAISE NOTICE 'Tenant encontrado: %', tenant_found;
    
    -- Passo 2: Definir contexto
    IF tenant_found IS NOT NULL THEN
        PERFORM app.set_current_tenant(tenant_found);
        context_set := true;
        RAISE NOTICE 'Contexto definido: %', tenant_found;
    END IF;
    
    -- Passo 3: Verificar contexto
    RAISE NOTICE 'Contexto atual: %', app.get_current_tenant_id();
    
    -- Passo 4: Buscar usuário
    IF context_set THEN
        BEGIN
            SELECT user_id, tenant_id, full_name INTO user_found
            FROM app.users 
            WHERE user_id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d';
            
            IF user_found IS NOT NULL THEN
                RAISE NOTICE 'Usuário encontrado: % (tenant: %)', user_found.full_name, user_found.tenant_id;
            ELSE
                RAISE NOTICE 'Usuário NÃO encontrado com contexto';
            END IF;
            
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'ERRO ao buscar usuário: %', SQLERRM;
        END;
    END IF;
END $$;

-- 8. VERIFICAR PERMISSÕES DO ROLE ANON
-- =====================================================
SELECT 
    'PERMISSÕES ANON' as check_type,
    grantee,
    table_schema,
    table_name,
    privilege_type
FROM information_schema.table_privileges 
WHERE table_schema = 'app' 
AND table_name = 'users'
AND grantee = 'anon';

-- 9. VERIFICAR SE POLÍTICA ESTÁ CORRETA
-- =====================================================
SELECT 
    'ANÁLISE DA POLÍTICA' as info,
    policyname,
    qual as condition,
    CASE 
        WHEN qual LIKE '%app.get_current_tenant_id()%' THEN 'USA FUNÇÃO CORRETA'
        WHEN qual LIKE '%get_current_tenant_id()%' THEN 'USA FUNÇÃO SEM SCHEMA'
        ELSE 'POLÍTICA DIFERENTE'
    END as analysis
FROM pg_policies 
WHERE schemaname = 'app'
AND tablename = 'users';

-- 10. TESTE FINAL: SIMULAR EXATAMENTE O QUE O CLIENTE FAZ
-- =====================================================

-- Limpar contexto
SELECT set_config('app.current_tenant_id', '', false);

-- Definir contexto como o cliente faz
SELECT app.set_current_tenant('1c11dd1a-97a8-45fd-a295-f56963f50f9a');

-- Verificar se contexto foi definido
SELECT 
    'TESTE FINAL' as teste,
    app.get_current_tenant_id() as tenant_context,
    CASE 
        WHEN app.get_current_tenant_id() IS NULL THEN 'CONTEXTO NULL'
        WHEN app.get_current_tenant_id()::text = '' THEN 'CONTEXTO VAZIO'
        WHEN app.get_current_tenant_id() = '1c11dd1a-97a8-45fd-a295-f56963f50f9a' THEN 'CONTEXTO CORRETO'
        ELSE 'CONTEXTO DIFERENTE'
    END as status;

-- Tentar buscar usuário
SELECT 
    'BUSCA FINAL' as teste,
    user_id,
    tenant_id
FROM app.users 
WHERE user_id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d';

-- =====================================================
-- RESULTADO ESPERADO:
-- - Funções existem no schema app
-- - RLS habilitado na tabela users
-- - Política usa app.get_current_tenant_id()
-- - Contexto é definido e mantido
-- - Usuário é encontrado com contexto
-- =====================================================
