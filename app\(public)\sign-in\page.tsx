import { Message } from "@/components/form-message";
import LoginFormClient from "@/components/auth/login-form-client";
import { LoginFormStatic } from "@/components/auth/static-forms";
import LayoutAuth from "../layout-auth";
import { MobileSafe } from "@/components/ui/mobile-safe";

export default async function Login(props: { searchParams: Promise<Message> }) {
  const searchParams = await props.searchParams;
  return (
    <LayoutAuth>
      <MobileSafe fallback={<LoginFormStatic />}>
        <LoginFormClient initialMessage={searchParams} />
      </MobileSafe>
    </LayoutAuth>
  );
}
