import { spawn } from 'child_process';
import { GoogleAuth } from 'google-auth-library';
import { secureLogger } from '@/lib/utils/secure-logger';
import { agentCostLogger } from '@/lib/agent-cost-logger';

interface VertexAIConfig {
  projectId: string;
  location: string;
  reasoningEngineId: string;
}

interface VertexAISession {
  id: string;
  user_id: string;
}

interface VertexAIResponse {
  id: string;
  content: {
    parts: Array<{
      text?: string;
      functionCall?: {
        name: string;
        args: any;
      };
    }>;
    role: 'model';
  };
  timestamp: number;
  isDebugAction?: boolean; // Para identificar ações de debug
  debugInfo?: {
    action: string;
    agentName?: string;
    details?: any;
  };
  rawData?: any; // Dados brutos para logging de custos
}

interface CachedToken {
  token: string;
  expiresAt: number; // timestamp em milliseconds
}

export class VertexAIHttpService {
  private config: VertexAIConfig;
  private baseUrl: string;
  private auth: GoogleAuth;
  private cachedToken: CachedToken | null = null;
  private tokenRefreshPromise: Promise<string> | null = null;
  private rawResponses: any[] = []; // Armazenar dados brutos para logging

  constructor(config: VertexAIConfig) {
    this.config = config;

    // URL base para a API do Vertex AI Reasoning Engine
    this.baseUrl = `https://${config.location}-aiplatform.googleapis.com/v1/projects/${config.projectId}/locations/${config.location}/reasoningEngines/${config.reasoningEngineId}`;

    // Configurar autenticação Google com suporte para Vercel
    this.auth = this.createGoogleAuth();

    // Inicializar token em background (não bloquear construtor)
    this.initializeTokenInBackground();
  }

  /**
   * Inicializa o token em background para acelerar primeira requisição
   */
  private initializeTokenInBackground(): void {
    // Não aguardar o resultado para não bloquear o construtor
    this.getAccessToken().catch(error => {
      console.log('⚠️ Falha na inicialização do token em background:', error.message);
    });
  }

  /**
   * Corrige o formato da chave privada para garantir quebras de linha corretas
   */
  private fixPrivateKeyFormat(privateKey: string): string {
    // Remover quebras de linha existentes e espaços extras
    let cleanKey = privateKey.replace(/\\n/g, '\n').trim();

    // Se não tem quebras de linha, adicionar
    if (!cleanKey.includes('\n')) {
      // Adicionar quebras de linha após header e antes footer
      cleanKey = cleanKey
        .replace('-----BEGIN PRIVATE KEY-----', '-----BEGIN PRIVATE KEY-----\n')
        .replace('-----END PRIVATE KEY-----', '\n-----END PRIVATE KEY-----');

      // Adicionar quebras de linha a cada 64 caracteres no meio
      const lines = [];
      const header = '-----BEGIN PRIVATE KEY-----';
      const footer = '-----END PRIVATE KEY-----';

      if (cleanKey.startsWith(header) && cleanKey.endsWith(footer)) {
        const keyContent = cleanKey
          .replace(header, '')
          .replace(footer, '')
          .replace(/\s/g, '');

        lines.push(header);

        // Dividir em linhas de 64 caracteres
        for (let i = 0; i < keyContent.length; i += 64) {
          lines.push(keyContent.substring(i, i + 64));
        }

        lines.push(footer);
        cleanKey = lines.join('\n');
      }
    }

    // ⚠️ REMOVIDO: Log de informações da chave privada - Risco de segurança
    // console.log('Chave privada formatada:', {
    //   length: cleanKey.length,
    //   hasNewlines: cleanKey.includes('\n'),
    //   startsCorrectly: cleanKey.startsWith('-----BEGIN PRIVATE KEY-----'),
    //   endsCorrectly: cleanKey.endsWith('-----END PRIVATE KEY-----')
    // });

    return cleanKey;
  }

  /**
   * Cria instância do GoogleAuth com suporte para diferentes ambientes
   */
  private createGoogleAuth(): GoogleAuth {
    // Vercel/Produção: Usar JSON da variável de ambiente
    if (process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON) {
      try {
        const credentials = JSON.parse(process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON);
        console.log('Usando credenciais JSON da variável de ambiente para Vercel');

        // Corrigir quebras de linha na private_key se necessário
        if (credentials.private_key) {
          credentials.private_key = this.fixPrivateKeyFormat(credentials.private_key);
        }

        // ⚠️ REMOVIDO: Log de credenciais - Risco de segurança
        // console.log('Credenciais processadas:', {
        //   type: credentials.type,
        //   project_id: credentials.project_id,
        //   client_email: credentials.client_email,
        //   private_key_id: credentials.private_key_id,
        //   private_key_length: credentials.private_key?.length || 0
        // });

        return new GoogleAuth({
          credentials,
          scopes: ['https://www.googleapis.com/auth/cloud-platform'],
        });
      } catch (error) {
        console.error('Erro ao parsear GOOGLE_APPLICATION_CREDENTIALS_JSON:', error);
      }
    }

    // Vercel/Produção: Usar variáveis individuais
    if (process.env.GOOGLE_CLIENT_EMAIL && process.env.GOOGLE_PRIVATE_KEY) {
      console.log('Usando credenciais individuais da variável de ambiente para Vercel');

      const privateKey = this.fixPrivateKeyFormat(process.env.GOOGLE_PRIVATE_KEY);

      // ⚠️ REMOVIDO: Log de credenciais individuais - Risco de segurança
      // console.log('Credenciais individuais processadas:', {
      //   client_email: process.env.GOOGLE_CLIENT_EMAIL,
      //   project_id: this.config.projectId,
      //   private_key_length: privateKey.length
      // });

      return new GoogleAuth({
        credentials: {
          client_email: process.env.GOOGLE_CLIENT_EMAIL,
          private_key: privateKey,
          project_id: this.config.projectId,
        },
        scopes: ['https://www.googleapis.com/auth/cloud-platform'],
      });
    }

    // Desenvolvimento: Usar Application Default Credentials
    console.log('Usando Application Default Credentials (desenvolvimento)');
    return new GoogleAuth({
      scopes: ['https://www.googleapis.com/auth/cloud-platform'],
    });
  }

  /**
   * Obtém o token de acesso com cache e renovação automática
   * Cache: 50 minutos (tokens expiram em ~60 minutos)
   * Renovação: Automática quando restam menos de 10 minutos
   */
  private async getAccessToken(): Promise<string> {
    const now = Date.now();

    // Verificar se temos um token válido em cache
    if (this.cachedToken && this.cachedToken.expiresAt > now) {
      const minutesLeft = Math.round((this.cachedToken.expiresAt - now) / (1000 * 60));
      console.log(`🚀 Usando token em cache (expira em ${minutesLeft} minutos)`);
      return this.cachedToken.token;
    }

    // Se já existe uma renovação em andamento, aguardar ela
    if (this.tokenRefreshPromise) {
      console.log('⏳ Aguardando renovação de token em andamento...');
      return await this.tokenRefreshPromise;
    }

    // Iniciar nova renovação
    console.log('🔄 Renovando token de acesso...');
    this.tokenRefreshPromise = this.refreshAccessToken();

    try {
      const newToken = await this.tokenRefreshPromise;
      return newToken;
    } finally {
      this.tokenRefreshPromise = null;
    }
  }

  /**
   * Renova o token de acesso usando múltiplas estratégias
   */
  private async refreshAccessToken(): Promise<string> {
    const startTime = Date.now();

    // Estratégia 1: Usar google-auth-library (Application Default Credentials)
    try {
      console.log('🔑 Tentando autenticação via google-auth-library...');
      const client = await this.auth.getClient();
      const accessToken = await client.getAccessToken();
      if (accessToken.token) {
        const authTime = Date.now() - startTime;
        console.log(`✅ Autenticação via google-auth-library bem-sucedida (${authTime}ms)`);

        // Cachear token por 50 minutos (tokens expiram em ~60 minutos)
        this.cachedToken = {
          token: accessToken.token,
          expiresAt: Date.now() + (50 * 60 * 1000) // 50 minutos
        };

        return accessToken.token;
      }
    } catch (error) {
      console.log('❌ Falha na autenticação via google-auth-library:', error instanceof Error ? error.message : 'Erro desconhecido');
    }

    // Estratégia 2: Usar gcloud CLI
    try {
      console.log('🔑 Tentando autenticação via gcloud CLI...');
      const token = await this.getTokenFromGcloud();
      const authTime = Date.now() - startTime;
      console.log(`✅ Autenticação via gcloud CLI bem-sucedida (${authTime}ms)`);

      // Cachear token por 50 minutos
      this.cachedToken = {
        token: token,
        expiresAt: Date.now() + (50 * 60 * 1000)
      };

      return token;
    } catch (error) {
      console.log('❌ Falha na autenticação via gcloud CLI:', error instanceof Error ? error.message : 'Erro desconhecido');
    }

    // Estratégia 3: Usar variável de ambiente (para desenvolvimento)
    const envToken = process.env.VERTEX_AI_ACCESS_TOKEN;
    if (envToken) {
      console.log('✅ Usando token da variável de ambiente VERTEX_AI_ACCESS_TOKEN');

      // Cachear por 30 minutos (tokens manuais podem ter TTL diferente)
      this.cachedToken = {
        token: envToken,
        expiresAt: Date.now() + (30 * 60 * 1000)
      };

      return envToken;
    }

    // Se todas as estratégias falharam
    throw new Error(`
Falha em todas as estratégias de autenticação. Tente uma das opções:

1. Configurar Application Default Credentials:
   gcloud auth application-default login

2. Definir variável de ambiente com chave de serviço:
   set GOOGLE_APPLICATION_CREDENTIALS=path\\to\\service-account-key.json

3. Instalar Google Cloud SDK e fazer login:
   gcloud auth login

4. Para desenvolvimento, definir token manualmente:
   set VERTEX_AI_ACCESS_TOKEN=your_access_token
`);
  }

  /**
   * Obtém token usando gcloud CLI
   */
  private async getTokenFromGcloud(): Promise<string> {
    return new Promise((resolve, reject) => {
      const gcloudProcess = spawn('gcloud', ['auth', 'print-access-token'], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      gcloudProcess.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      gcloudProcess.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      gcloudProcess.on('close', (code) => {
        if (code !== 0) {
          reject(new Error(`gcloud auth falhou com código ${code}: ${stderr}`));
          return;
        }

        const token = stdout.trim();
        if (!token) {
          reject(new Error('Token de acesso vazio. Execute: gcloud auth application-default login'));
          return;
        }

        resolve(token);
      });

      gcloudProcess.on('error', (error) => {
        reject(new Error(`Erro ao executar gcloud: ${error.message}. Google Cloud SDK não está instalado ou não está no PATH.`));
      });
    });
  }

  /**
   * Cria uma nova sessão no Vertex AI Reasoning Engine
   * Equivalente ao curl para create_session
   */
  async createSession(userId: string): Promise<VertexAISession> {
    try {
      const accessToken = await this.getAccessToken();

      console.log('Criando sessão Vertex AI para usuário:', userId);

      const response = await fetch(`${this.baseUrl}:query`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          class_method: "create_session",
          input: {
            user_id: userId
          }
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const result = await response.json();

      console.log('Resposta da criação de sessão:', result);

      // Extrair o session_id da resposta
      let sessionId: string;
      if (result.output && result.output.id) {
        sessionId = result.output.id;
      } else if (result.output && result.output.session_id) {
        sessionId = result.output.session_id;
      } else if (result.session_id) {
        sessionId = result.session_id;
      } else if (typeof result.output === 'string') {
        // Se a resposta for uma string, tentar extrair o session_id
        const match = result.output.match(/session_id['":\s]*([^'",\s}]+)/);
        sessionId = match ? match[1] : `session_${userId}_${Date.now()}`;
      } else {
        // Fallback: gerar um ID único
        sessionId = `session_${userId}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      }

      console.log('Sessão Vertex AI criada com sucesso:', sessionId);

      return {
        id: sessionId,
        user_id: userId
      };
    } catch (error) {
      console.error('Erro ao criar sessão Vertex AI:', error);
      throw new Error(`Falha ao criar sessão: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }

  /**
   * Envia uma mensagem para o agente no Vertex AI Reasoning Engine
   * Usa o endpoint correto :streamQuery para stream_query
   */
  async sendMessage(
    userId: string,
    sessionId: string,
    message: string,
    streaming: boolean = true
  ): Promise<VertexAIResponse[]> {
    try {
      const accessToken = await this.getAccessToken();

      // Limpar dados brutos anteriores
      this.rawResponses = [];

      // ⚠️ REMOVIDO: Log de dados da mensagem - Pode expor informações sensíveis
      // console.log('Enviando mensagem Vertex AI:', {
      //   userId,
      //   sessionId,
      //   message,
      //   streaming
      // });

      let responses: VertexAIResponse[];
      if (streaming) {
        responses = await this.streamQuery(accessToken, userId, sessionId, message);
      } else {
        responses = await this.query(accessToken, userId, sessionId, message);
      }

      // Fazer logging dos custos usando os dados brutos (NOVA IMPLEMENTAÇÃO)
      try {
        console.log('🚀 Chamando logCosts com:', { userId, sessionId });
        const logResult = await this.logCosts(userId, sessionId);
        console.log('✅ logCosts concluído:', logResult);
      } catch (error) {
        console.error('❌ Erro ao registrar custos:', error);
      }

      return responses;
    } catch (error) {
      console.error('Erro ao enviar mensagem Vertex AI:', error);
      throw new Error(`Falha ao enviar mensagem: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }

  /**
   * Executa uma stream query usando Server-Sent Events
   * Usa o endpoint :streamQuery correto
   */
  private async streamQuery(
    accessToken: string,
    userId: string,
    sessionId: string,
    message: string
  ): Promise<VertexAIResponse[]> {
    console.log('Usando endpoint :streamQuery para stream_query');

    const response = await fetch(`${this.baseUrl}:streamQuery?alt=sse`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        class_method: "stream_query",
        input: {
          user_id: userId,
          session_id: sessionId,
          message: message
        }
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const responses: VertexAIResponse[] = [];

    // Processar o stream de resposta (Server-Sent Events)
    if (response.body) {
      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n').filter(line => line.trim());

          for (const line of lines) {
            try {
              // Processar linhas de Server-Sent Events
              if (line.startsWith('data: ')) {
                const eventData = line.substring(6);
                if (eventData.trim() === '[DONE]') {
                  break;
                }

                const parsedData = JSON.parse(eventData);
                console.log('Evento SSE recebido:', parsedData);

                // Armazenar dados brutos para logging de custos
                this.rawResponses.push(parsedData);
                console.log('📊 Dados brutos armazenados:', {
                  total: this.rawResponses.length,
                  hasUsageMetadata: !!parsedData.usage_metadata,
                  hasInvocationId: !!parsedData.invocation_id,
                  hasAuthor: !!parsedData.author,
                  role: parsedData.content?.role
                });

                const processedEvents = this.processResponse(parsedData);
                responses.push(...processedEvents);
              }
            } catch (parseError) {
              console.warn('Erro ao processar linha SSE:', line, parseError);
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    }

    return responses;
  }

  /**
   * Executa uma query simples (não-streaming)
   * Usa o endpoint :streamQuery com ?alt=sse e processa como stream
   */
  private async query(
    accessToken: string,
    userId: string,
    sessionId: string,
    message: string
  ): Promise<VertexAIResponse[]> {
    console.log('Usando endpoint :streamQuery com SSE para query não-streaming');

    const response = await fetch(`${this.baseUrl}:streamQuery?alt=sse`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        class_method: "stream_query",
        input: {
          user_id: userId,
          session_id: sessionId,
          message: message
        }
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const responses: VertexAIResponse[] = [];

    // Processar como stream mesmo para query não-streaming
    if (response.body) {
      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      try {
        let buffer = '';

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');

          // Manter a última linha incompleta no buffer
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.trim()) {
              try {
                // Processar linhas de Server-Sent Events
                if (line.startsWith('data: ')) {
                  const eventData = line.substring(6).trim();
                  if (eventData === '[DONE]') {
                    console.log('Stream finalizado com [DONE]');
                    break;
                  }

                  if (eventData) {
                    const parsedData = JSON.parse(eventData);
                    console.log('Evento SSE recebido (query):', parsedData);

                    // Armazenar dados brutos para logging de custos
                    this.rawResponses.push(parsedData);

                    const processedEvents = this.processResponse(parsedData);
                    responses.push(...processedEvents);
                  }
                } else if (line.startsWith('{') || line.startsWith('[')) {
                  // Tentar processar como JSON direto
                  try {
                    const parsedData = JSON.parse(line);
                    console.log('JSON direto recebido (query):', parsedData);

                    // Armazenar dados brutos para logging de custos
                    this.rawResponses.push(parsedData);

                    const processedEvents = this.processResponse(parsedData);
                    responses.push(...processedEvents);
                  } catch (jsonError) {
                    console.warn('Linha não é JSON válido:', line);
                  }
                }
              } catch (parseError) {
                console.warn('Erro ao processar linha SSE (query):', line, parseError);
              }
            }
          }
        }

        // Processar qualquer conteúdo restante no buffer
        if (buffer.trim()) {
          try {
            if (buffer.startsWith('data: ')) {
              const eventData = buffer.substring(6).trim();
              if (eventData && eventData !== '[DONE]') {
                const parsedData = JSON.parse(eventData);
                console.log('Evento SSE final (query):', parsedData);

                // Armazenar dados brutos para logging de custos
                this.rawResponses.push(parsedData);

                const processedEvents = this.processResponse(parsedData);
                responses.push(...processedEvents);
              }
            }
          } catch (parseError) {
            console.warn('Erro ao processar buffer final:', buffer, parseError);
          }
        }
      } finally {
        reader.releaseLock();
      }
    }

    console.log(`Query não-streaming processada: ${responses.length} respostas`);
    return responses;
  }



  /**
   * Deleta uma sessão no Vertex AI Reasoning Engine
   * Equivalente ao curl para delete_session
   */
  async deleteSession(userId: string, sessionId: string): Promise<void> {
    try {
      const accessToken = await this.getAccessToken();

      console.log('Deletando sessão Vertex AI:', { userId, sessionId });

      const response = await fetch(`${this.baseUrl}:query`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          class_method: "delete_session",
          input: {
            user_id: userId,
            session_id: sessionId
          }
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const result = await response.json();
      console.log('Sessão Vertex AI deletada com sucesso:', result);
    } catch (error) {
      console.error('Erro ao deletar sessão Vertex AI:', error);
      throw new Error(`Falha ao deletar sessão: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }

  /**
   * Processa a resposta do Vertex AI no formato esperado
   * Extrai texto limpo e identifica ações de debug
   */
  private processResponse(data: any): VertexAIResponse[] {
    if (!data) return [];

    // Se for um array, processar cada item
    if (Array.isArray(data)) {
      return data.flatMap(item => this.processResponse(item));
    }

    // Se for uma string
    if (typeof data === 'string') {
      return [{
        id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        content: {
          parts: [{ text: data }],
          role: 'model'
        },
        timestamp: Date.now()
      }];
    }

    const responses: VertexAIResponse[] = [];

    // Extrair texto da resposta (sempre processar para respostas de texto)
    let extractedText = '';

    // Prioridade 1: content.parts[0].text (formato padrão do agente)
    if (data.content?.parts?.[0]?.text) {
      extractedText = data.content.parts[0].text;
    }
    // Prioridade 2: actions.state_delta.query_result (resultado da query)
    else if (data.actions?.state_delta?.query_result) {
      extractedText = data.actions.state_delta.query_result;
    }
    // Prioridade 3: text direto
    else if (data.text) {
      extractedText = data.text;
    }
    // Prioridade 4: message
    else if (data.message) {
      extractedText = data.message;
    }
    // Prioridade 5: output
    else if (data.output) {
      extractedText = typeof data.output === 'string' ? data.output : JSON.stringify(data.output);
    }

    // Limpar o texto (remover quebras de linha extras, etc.)
    const cleanText = extractedText.trim().replace(/\n+/g, '\n');

    if (cleanText) {
      console.log('Texto extraído da resposta:', cleanText.substring(0, 100) + (cleanText.length > 100 ? '...' : ''));

      responses.push({
        id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        content: {
          parts: [{ text: cleanText }],
          role: 'model'
        },
        timestamp: Date.now()
      });
    }

    return responses;
  }

  /**
   * Faz logging dos custos usando os dados brutos coletados (NOVA IMPLEMENTAÇÃO)
   * Consome apenas 1 crédito por invocação, independente do número de agentes
   */
  private async logCosts(userId: string, sessionId: string): Promise<{ success: boolean; remainingCredits: number; error?: string }> {
    try {
      console.log('🔍 Iniciando logging de custos...');
      console.log('📊 Total de respostas brutas coletadas:', this.rawResponses.length);

      // Log detalhado de cada resposta
      this.rawResponses.forEach((response, index) => {
        console.log(`📋 Resposta ${index + 1}:`, {
          hasUsageMetadata: !!response.usage_metadata,
          hasInvocationId: !!response.invocation_id,
          hasAuthor: !!response.author,
          role: response.content?.role,
          tokensInfo: response.usage_metadata ? {
            prompt: response.usage_metadata.prompt_token_count,
            candidates: response.usage_metadata.candidates_token_count,
            thoughts: response.usage_metadata.thoughts_token_count
          } : 'N/A'
        });
      });

      // Usar a nova função que consome apenas 1 crédito por invocação
      const result = await agentCostLogger.logInvocationResponses(
        this.rawResponses,
        userId,
        sessionId
      );

      return result;
    } catch (error) {
      console.error('❌ Erro no logging de custos:', error);
      return {
        success: false,
        remainingCredits: 0,
        error: 'Erro interno no logging'
      };
    }
  }


}

// Singleton para compartilhar cache de token entre requisições
let vertexAIServiceInstance: VertexAIHttpService | null = null;

// Factory function para criar/reutilizar instância do serviço
export function createVertexAIHttpService(): VertexAIHttpService {
  // Reutilizar instância existente para manter cache de token
  if (vertexAIServiceInstance) {
    return vertexAIServiceInstance;
  }

  const config: VertexAIConfig = {
    projectId: process.env.VERTEX_AI_PROJECT_ID!,
    location: process.env.VERTEX_AI_LOCATION!,
    reasoningEngineId: process.env.VERTEX_AI_REASONING_ENGINE_ID!,
  };

  // Validar configuração
  if (!config.projectId || !config.location || !config.reasoningEngineId) {
    throw new Error('Configuração do Vertex AI incompleta. Verifique as variáveis de ambiente.');
  }

  // Criar nova instância e cachear
  vertexAIServiceInstance = new VertexAIHttpService(config);
  console.log('🏗️ Nova instância do VertexAIHttpService criada com cache de token');

  return vertexAIServiceInstance;
}
