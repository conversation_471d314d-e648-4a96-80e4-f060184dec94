import { createClient } from '@supabase/supabase-js';

export class CreditsService {
  private supabase;

  constructor() {
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY!
    );
  }

  /**
   * Busca os créditos atuais do usuário
   */
  async getUserCredits(userId: string): Promise<number> {
    try {
      const { data, error } = await this.supabase
        .schema('app')
        .from('users')
        .select('credits')
        .eq('user_id', userId)
        .single();

      if (error) {
        console.error('Erro ao buscar créditos do usuário:', error);
        return 0;
      }

      return data?.credits || 0;
    } catch (error) {
      console.error('Erro ao buscar créditos:', error);
      return 0;
    }
  }

  /**
   * Verifica se o usuário tem créditos suficientes
   */
  async hasCredits(userId: string, requiredCredits: number = 1): Promise<boolean> {
    const currentCredits = await this.getUserCredits(userId);
    return currentCredits >= requiredCredits;
  }

  /**
   * Consome créditos do usuário (transação atômica)
   * Retorna os créditos restantes após o consumo
   */
  async consumeCredits(
    userId: string, 
    creditsToConsume: number = 1,
    invocationId?: string
  ): Promise<{ success: boolean; remainingCredits: number; error?: string }> {
    try {
      // Buscar créditos atuais
      const { data: currentData, error: fetchError } = await this.supabase
        .schema('app')
        .from('users')
        .select('credits')
        .eq('user_id', userId)
        .single();

      if (fetchError) {
        return { 
          success: false, 
          remainingCredits: 0, 
          error: 'Erro ao buscar créditos atuais' 
        };
      }

      const currentCredits = currentData?.credits || 0;

      // Verificar se tem créditos suficientes
      if (currentCredits < creditsToConsume) {
        return { 
          success: false, 
          remainingCredits: currentCredits, 
          error: 'Créditos insuficientes' 
        };
      }

      const newCredits = currentCredits - creditsToConsume;

      // Atualizar créditos do usuário
      const { error: updateError } = await this.supabase
        .schema('app')
        .from('users')
        .update({ credits: newCredits, updated_at: new Date().toISOString() })
        .eq('user_id', userId);

      if (updateError) {
        return { 
          success: false, 
          remainingCredits: currentCredits, 
          error: 'Erro ao atualizar créditos' 
        };
      }

      return { 
        success: true, 
        remainingCredits: newCredits 
      };

    } catch (error) {
      console.error('Erro ao consumir créditos:', error);
      return { 
        success: false, 
        remainingCredits: 0, 
        error: 'Erro interno' 
      };
    }
  }

  /**
   * Adiciona créditos ao usuário
   */
  async addCredits(
    userId: string, 
    creditsToAdd: number
  ): Promise<{ success: boolean; newCredits: number; error?: string }> {
    try {
      // Buscar créditos atuais
      const { data: currentData, error: fetchError } = await this.supabase
        .schema('app')
        .from('users')
        .select('credits')
        .eq('user_id', userId)
        .single();

      if (fetchError) {
        return { 
          success: false, 
          newCredits: 0, 
          error: 'Erro ao buscar créditos atuais' 
        };
      }

      const currentCredits = currentData?.credits || 0;
      const newCredits = currentCredits + creditsToAdd;

      // Atualizar créditos
      const { error: updateError } = await this.supabase
        .schema('app')
        .from('users')
        .update({ credits: newCredits, updated_at: new Date().toISOString() })
        .eq('user_id', userId);

      if (updateError) {
        return { 
          success: false, 
          newCredits: currentCredits, 
          error: 'Erro ao atualizar créditos' 
        };
      }

      return { 
        success: true, 
        newCredits 
      };

    } catch (error) {
      console.error('Erro ao adicionar créditos:', error);
      return { 
        success: false, 
        newCredits: 0, 
        error: 'Erro interno' 
      };
    }
  }

  /**
   * Cria ou atualiza registro do usuário na tabela users
   */
  async ensureUserExists(userId: string, initialCredits: number = 10): Promise<boolean> {
    try {
      // Verificar se usuário já existe
      const { data: existingUser } = await this.supabase
        .schema('app')
        .from('users')
        .select('user_id')
        .eq('user_id', userId)
        .single();

      if (existingUser) {
        return true; // Usuário já existe
      }

      // Criar usuário com créditos iniciais
      const { error } = await this.supabase
        .schema('app')
        .from('users')
        .insert([{
          user_id: userId,
          credits: initialCredits,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }]);

      if (error) {
        console.error('Erro ao criar usuário:', error);
        return false;
      }

      return true;

    } catch (error) {
      console.error('Erro ao verificar/criar usuário:', error);
      return false;
    }
  }
}

// Instância singleton
export const creditsService = new CreditsService();
