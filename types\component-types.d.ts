// Declaração de tipos para componentes de chat
declare module '@/components/chat/chat-interface' {
  interface ChatInterfaceProps {
    userId: string;
  }
  const ChatInterface: React.FC<ChatInterfaceProps>;
  export default ChatInterface;
}

declare module '@/components/chat/chat-message' {
  import { ChatMessage as ChatMessageType } from '@/components/chat/types';

  interface ChatMessageProps {
    message: ChatMessageType;
  }

  const ChatMessage: React.ForwardRefExoticComponent<
    ChatMessageProps & React.RefAttributes<HTMLDivElement>
  >;

  export default ChatMessage;
}

declare module '@/components/chat/types' {
  export interface ChatMessage {
    id: string;
    content: {
      text: string;
      image?: string;
      echarts?: any; // Dados de configuração do ECharts
    };
    role: "user" | "assistant" | "system";
    timestamp: string;
  }

  export interface ApiResponsePart {
    text?: string;
    functionCall?: {
      name: string;
      args: {
        [key: string]: any;
      };
    };
    functionResponse?: {
      name: string;
      response: {
        result: string;
      };
    };
  }

  export interface ApiResponseContent {
    parts: ApiResponsePart[];
    role: "user" | "model";
  }

  export interface ApiResponse {
    id: string;
    content: ApiResponseContent;
    timestamp: number;
    invocation_id: string;
    author: string;
  }
}

// Declaração de tipos para layout
declare module './layout' {
  import { ReactNode } from 'react';

  interface ProtectedLayoutProps {
    children: ReactNode;
  }

  const ProtectedLayout: React.FC<ProtectedLayoutProps>;
  export default ProtectedLayout;
}