# 🔍 DEBUG: An<PERSON><PERSON><PERSON> da URL de Query

## 🚨 PROBLEMA IDENTIFICADO

A URL que está sendo gerada está incorreta:
```
GET https://opawvlqvsbltimqlzbdo.supabase.co/rest/v1/users?select=tenant_id&user_id=eq.38cc3c24-7d83-4194-bcc9-68a501ceff2d
```

## ❌ PROBLEMAS NA URL:

### 1. **Schema Missing**
- **Problema**: URL não especifica o schema 'app'
- **URL Atual**: `/rest/v1/users`
- **URL Correta**: `/rest/v1/users` (com header `Accept-Profile: app`)

### 2. **Possível Problema de Permissões**
- **Problema**: Role 'anon' pode não ter permissões na tabela app.users
- **Status**: 400 Bad Request sugere problema de permissões ou estrutura

## 🔧 POSSÍVEIS CAUSAS:

### Causa 1: Tabela não existe no schema 'app'
```sql
-- Verificar se tabela existe
SELECT * FROM information_schema.tables 
WHERE table_schema = 'app' AND table_name = 'users';
```

### Causa 2: Role 'anon' sem permissões
```sql
-- Verificar permissões
SELECT * FROM information_schema.table_privileges 
WHERE table_schema = 'app' AND table_name = 'users' AND grantee = 'anon';
```

### Causa 3: RLS bloqueando sem contexto
```sql
-- Verificar RLS
SELECT rowsecurity FROM pg_tables 
WHERE schemaname = 'app' AND tablename = 'users';
```

## 🛠️ CORREÇÕES NECESSÁRIAS:

### 1. **Verificar Estrutura da Tabela**
Execute: `scripts/verificar-estrutura-users.sql`

### 2. **Verificar Permissões**
```sql
-- Dar permissões ao role anon se necessário
GRANT SELECT, INSERT, UPDATE ON app.users TO anon;
```

### 3. **Verificar RLS**
```sql
-- Verificar se políticas RLS estão corretas
SELECT * FROM pg_policies WHERE schemaname = 'app' AND tablename = 'users';
```

## 🧪 TESTES PARA EXECUTAR:

### 1. **Teste Manual no Supabase**
```sql
-- No SQL Editor, teste:
SELECT tenant_id FROM app.users WHERE user_id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d';
```

### 2. **Teste com Contexto**
```sql
-- Definir contexto e testar
SELECT app.set_current_tenant('1c11dd1a-97a8-45fd-a295-f56963f50f9a');
SELECT tenant_id FROM app.users WHERE user_id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d';
```

### 3. **Teste de Permissões**
```sql
-- Verificar se anon pode acessar
SET ROLE anon;
SELECT tenant_id FROM app.users WHERE user_id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d';
RESET ROLE;
```

## 📋 CHECKLIST DE VERIFICAÇÃO:

- [ ] Tabela app.users existe
- [ ] Usuário existe na tabela
- [ ] Role anon tem permissões SELECT
- [ ] RLS está configurado corretamente
- [ ] Políticas RLS permitem acesso com contexto
- [ ] Contexto de tenant está sendo definido

## 🎯 PRÓXIMOS PASSOS:

1. **Execute o script de verificação**
2. **Analise os logs detalhados**
3. **Teste login novamente**
4. **Verifique console do navegador**

---

**Os logs adicionados vão mostrar exatamente onde está o problema! 🔍**
