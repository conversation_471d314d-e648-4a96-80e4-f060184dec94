# 🔧 DEBUG MULTI-TENANT - SCHEMA 'app'

## Problema Identificado
O sistema está tentando acessar `demo.localhost` mas está sendo redirecionado para `/tenant-not-found`, indicando que não consegue ler a tabela `tenants` no schema correto.

## ✅ Correções Aplicadas

### 1. <PERSON><PERSON> as consultas SQL atualizadas para schema 'app'
- `utils/supabase/middleware.ts` ✅
- `app/api/tenant/current/route.ts` ✅  
- `app/api/admin/tenants/route.ts` ✅
- `sql/multitenant-setup.sql` ✅

### 2. Logs de debug adicionados ao middleware
- Console logs para rastrear o fluxo
- Verificação de erros na consulta
- Logs do RLS

## 🚀 Passos para Resolver

### 1. Execute o Script SQL Atualizado
```sql
-- No Supabase SQL Editor, execute:
-- Copie TODO o conteúdo de sql/multitenant-setup.sql
```

### 2. Teste a Configuração
```sql
-- Execute no Supabase SQL Editor:
-- Copie TODO o conteúdo de scripts/test-tenant.sql
```

### 3. Verificar Logs do Middleware
1. Acesse `http://demo.localhost:3000`
2. Abra o terminal onde o Next.js está rodando
3. Procure por logs que começam com `[MIDDLEWARE]`

### 4. Resultados Esperados nos Logs
```
[MIDDLEWARE] Subdomínio detectado: demo
[MIDDLEWARE] Resultado da consulta: { tenant: { id: '...', name: 'Empresa Demo', is_active: true }, tenantError: null }
[MIDDLEWARE] Tenant encontrado: Empresa Demo (ID: ...)
[MIDDLEWARE] Tenant definido no contexto RLS: ...
```

### 5. Se Ainda Não Funcionar

#### Verificar se a tabela existe:
```sql
SELECT EXISTS (
   SELECT FROM information_schema.tables 
   WHERE table_schema = 'app' 
   AND table_name = 'tenants'
);
```

#### Verificar se o tenant demo existe:
```sql
SELECT * FROM app.tenants WHERE subdomain = 'demo';
```

#### Verificar permissões do schema:
```sql
SELECT schema_name, schema_owner 
FROM information_schema.schemata 
WHERE schema_name = 'app';
```

## 🔍 Troubleshooting

### Erro: "relation app.tenants does not exist"
**Solução**: Execute o script SQL completo no Supabase

### Erro: "permission denied for schema app"
**Solução**: Verifique se o usuário tem permissões no schema 'app'

### Erro: "function set_current_tenant does not exist"
**Solução**: Execute a parte do script que cria as funções

### Logs mostram tenantError
**Solução**: Verifique o erro específico e as permissões

## 📝 Comandos de Verificação

### No Supabase SQL Editor:
```sql
-- 1. Verificar schema
\dn app

-- 2. Verificar tabelas no schema app
SELECT table_name FROM information_schema.tables WHERE table_schema = 'app';

-- 3. Verificar tenant demo
SELECT id, subdomain, name, is_active FROM app.tenants WHERE subdomain = 'demo';

-- 4. Verificar RLS
SELECT tablename, rowsecurity FROM pg_tables WHERE schemaname = 'app';

-- 5. Testar função
SELECT get_current_tenant_id();
```

### No Terminal do Next.js:
```bash
# Verificar logs em tempo real
npm run dev

# Em outro terminal, testar:
curl -H "Host: demo.localhost" http://localhost:3000
```

## 🎯 Próximos Passos

1. **Execute o script SQL atualizado**
2. **Execute o script de teste**
3. **Verifique os logs do middleware**
4. **Teste demo.localhost:3000**

Se ainda houver problemas, compartilhe:
- Os logs do middleware
- O resultado do script de teste
- Mensagens de erro específicas

## 📋 Checklist de Verificação

- [ ] Script SQL executado no Supabase
- [ ] Tabela `app.tenants` existe
- [ ] Tenant 'demo' existe e está ativo
- [ ] RLS habilitado
- [ ] Funções criadas
- [ ] Logs do middleware aparecem
- [ ] demo.localhost:3000 funciona

---

**IMPORTANTE**: Todos os arquivos foram atualizados para usar o schema 'app'. O problema deve ser resolvido após executar o script SQL atualizado.
