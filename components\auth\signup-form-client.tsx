"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { toast } from "sonner";
import { signUpClient } from "@/app/client-actions";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { FormMessage, Message } from "@/components/form-message";

export default function SignupFormClient({ initialMessage }: { initialMessage?: Message }) {
  const router = useRouter();
  const [message, setMessage] = useState<Message | null>(initialMessage || null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsSubmitting(true);

    try {
      const formData = new FormData(event.currentTarget);
      const result = await signUpClient(formData);

      if (result.success) {
        const successMessage = result.message || "Cadastro realizado com sucesso!";

        // Toast de sucesso
        toast.success("Conta criada!", {
          description: successMessage,
          duration: 5000,
        });

        setMessage({
          success: successMessage
        });
      }
    } catch (error: any) {
      const errorMessage = error.message || "Falha ao criar conta";

      // Definir mensagem de erro mais específica
      let userFriendlyMessage = errorMessage;
      if (errorMessage.includes("User already registered")) {
        userFriendlyMessage = "Este email já está cadastrado";
      } else if (errorMessage.includes("Password should be at least")) {
        userFriendlyMessage = "A senha deve ter pelo menos 6 caracteres";
      } else if (errorMessage.includes("Invalid email")) {
        userFriendlyMessage = "Email inválido";
      }

      // Toast de erro
      toast.error("Erro no cadastro", {
        description: userFriendlyMessage,
        duration: 4000,
        action: {
          label: "Tentar novamente",
          onClick: () => {
            setMessage(null);
          }
        }
      });

      setMessage({
        error: userFriendlyMessage
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form className="w-full flex flex-col" onSubmit={handleSubmit}>
      <h1 className="text-2xl font-medium text-center mb-2">Criar conta</h1>
      <p className="text-sm text-foreground text-center mb-8">
        Já tem uma conta?{" "}
        <Link className="text-primary underline" href="/sign-in">
          Entrar
        </Link>
      </p>
      <div className="flex flex-col gap-4">
        <div className="space-y-2">
          <Label htmlFor="full_name">Nome completo</Label>
          <Input name="full_name" placeholder="Seu nome" required />
        </div>
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input name="email" placeholder="<EMAIL>" required />
        </div>
        <div className="space-y-2">
          <Label htmlFor="password">Senha</Label>
          <Input
            type="password"
            name="password"
            placeholder="Sua senha"
            required
          />
        </div>
        <div className="pt-4">
          <SubmitButton
            className="w-full"
            pendingText="Criando conta..."
            disabled={isSubmitting}
          >
            Criar conta
          </SubmitButton>
        </div>
        {message && <FormMessage message={message} />}
      </div>
    </form>
  );
}
