/**
 * Sistema de logging seguro para produção
 * Remove automaticamente logs sensíveis em produção
 */

import { AppConfig } from '@/lib/config/app-config';

type LogLevel = 'debug' | 'info' | 'warn' | 'error';
type LogContext = 'API' | 'CHAT' | 'CHARTS' | 'COST' | 'AUTH' | 'GENERAL';

interface LogOptions {
  context?: LogContext;
  sanitize?: boolean;
  forceLog?: boolean; // Força log mesmo em produção (para erros críticos)
}

class ProductionLogger {
  private isDevelopment = process.env.NODE_ENV === 'development';
  private isProduction = process.env.NODE_ENV === 'production';

  /**
   * Verifica se deve logar baseado no contexto e configuração
   */
  private shouldLog(context: LogContext, level: LogLevel, forceLog = false): boolean {
    // Sempre logar erros críticos
    if (forceLog || level === 'error') {
      return true;
    }

    // Em desenvolvimento, sempre logar
    if (this.isDevelopment) {
      return true;
    }

    // Em produção, verificar configurações específicas
    if (this.isProduction) {
      // Se logs de produção estão explicitamente habilitados
      if (AppConfig.logging.enableProductionLogs) {
        return true;
      }

      // Verificar configurações específicas por contexto
      switch (context) {
        case 'API':
          return AppConfig.logging.enableApiLogs;
        case 'CHAT':
          return AppConfig.logging.enableChatLogs;
        case 'CHARTS':
          return AppConfig.logging.enableChartsLogs;
        case 'COST':
          return AppConfig.logging.enableCostLogs;
        default:
          return false;
      }
    }

    return true; // Outros ambientes (staging, etc.)
  }

  /**
   * Sanitiza dados sensíveis
   */
  private sanitizeData(data: any): any {
    if (typeof data === 'string') {
      // Remover URLs do Supabase
      if (data.includes('supabase.co') || data.includes('supabase.io')) {
        return '[SUPABASE_URL_REMOVED]';
      }
      
      // Remover tokens/keys
      if (data.length > 20 && (data.includes('eyJ') || data.includes('sb-'))) {
        return '[TOKEN_REMOVED]';
      }
      
      return data;
    }

    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeData(item));
    }

    if (data && typeof data === 'object') {
      const sanitized: any = {};
      
      for (const [key, value] of Object.entries(data)) {
        const lowerKey = key.toLowerCase();
        
        // Campos sensíveis para mascarar
        if (lowerKey.includes('token') || 
            lowerKey.includes('key') || 
            lowerKey.includes('password') || 
            lowerKey.includes('secret') ||
            lowerKey.includes('credential')) {
          sanitized[key] = '[MASKED]';
        } else if (lowerKey.includes('user_id') || lowerKey.includes('session_id')) {
          // Mascarar IDs parcialmente
          sanitized[key] = typeof value === 'string' && value.length > 8 
            ? `${value.substring(0, 4)}***${value.substring(value.length - 4)}`
            : '[MASKED_ID]';
        } else {
          sanitized[key] = this.sanitizeData(value);
        }
      }
      
      return sanitized;
    }

    return data;
  }

  /**
   * Formata mensagem com contexto
   */
  private formatMessage(message: string, context?: LogContext): string {
    const contextStr = context ? `[${context}] ` : '';
    return `${contextStr}${message}`;
  }

  /**
   * Log de debug (apenas desenvolvimento)
   */
  debug(message: string, data?: any, options: LogOptions = {}): void {
    if (!this.shouldLog(options.context || 'GENERAL', 'debug', options.forceLog)) {
      return;
    }

    const formattedMessage = this.formatMessage(message, options.context);
    const sanitizedData = options.sanitize !== false ? this.sanitizeData(data) : data;

    if (data) {
      console.debug(formattedMessage, sanitizedData);
    } else {
      console.debug(formattedMessage);
    }
  }

  /**
   * Log de informação
   */
  info(message: string, data?: any, options: LogOptions = {}): void {
    if (!this.shouldLog(options.context || 'GENERAL', 'info', options.forceLog)) {
      return;
    }

    const formattedMessage = this.formatMessage(message, options.context);
    const sanitizedData = options.sanitize !== false ? this.sanitizeData(data) : data;

    if (data) {
      console.info(formattedMessage, sanitizedData);
    } else {
      console.info(formattedMessage);
    }
  }

  /**
   * Log de aviso
   */
  warn(message: string, data?: any, options: LogOptions = {}): void {
    if (!this.shouldLog(options.context || 'GENERAL', 'warn', options.forceLog)) {
      return;
    }

    const formattedMessage = this.formatMessage(message, options.context);
    const sanitizedData = options.sanitize !== false ? this.sanitizeData(data) : data;

    if (data) {
      console.warn(formattedMessage, sanitizedData);
    } else {
      console.warn(formattedMessage);
    }
  }

  /**
   * Log de erro (sempre logado)
   */
  error(message: string, error?: any, options: LogOptions = {}): void {
    const formattedMessage = this.formatMessage(message, options.context);
    
    // Para erros, sanitizar apenas se explicitamente solicitado
    const errorData = options.sanitize === true ? this.sanitizeData(error) : error;

    if (error) {
      console.error(formattedMessage, errorData);
    } else {
      console.error(formattedMessage);
    }
  }

  /**
   * Log específico para API
   */
  api(message: string, data?: any, options: Omit<LogOptions, 'context'> = {}): void {
    this.info(message, data, { ...options, context: 'API' });
  }

  /**
   * Log específico para Chat
   */
  chat(message: string, data?: any, options: Omit<LogOptions, 'context'> = {}): void {
    this.info(message, data, { ...options, context: 'CHAT' });
  }

  /**
   * Log específico para Charts
   */
  charts(message: string, data?: any, options: Omit<LogOptions, 'context'> = {}): void {
    this.info(message, data, { ...options, context: 'CHARTS' });
  }

  /**
   * Log específico para Cost Logger
   */
  cost(message: string, data?: any, options: Omit<LogOptions, 'context'> = {}): void {
    this.info(message, data, { ...options, context: 'COST' });
  }
}

// Singleton instance
export const productionLogger = new ProductionLogger();

// Convenience exports
export const pLogger = productionLogger;
export default productionLogger;
