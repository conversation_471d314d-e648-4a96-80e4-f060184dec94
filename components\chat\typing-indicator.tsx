"use client";

import { forwardRef } from "react";
import { Bo<PERSON> } from "lucide-react";

interface TypingIndicatorProps {
  className?: string;
}

const TypingIndicator = forwardRef<HTMLDivElement, TypingIndicatorProps>(
  ({ className = "" }, ref) => {
    return (
      <div
        ref={ref}
        className={`flex gap-3 justify-start ${className}`}
      >
        <div className="flex-shrink-0">
          <div className="h-8 w-8 bg-primary/10 rounded-full flex items-center justify-center">
            <Bot className="h-5 w-5 text-primary" />
          </div>
        </div>
        <div className="flex-1 max-w-[80%] rounded-lg p-4 bg-muted">
          <div className="flex items-center space-x-3">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce [animation-delay:0ms] [animation-duration:1400ms]"></div>
              <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce [animation-delay:200ms] [animation-duration:1400ms]"></div>
              <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce [animation-delay:400ms] [animation-duration:1400ms]"></div>
            </div>
            <span className="text-sm text-muted-foreground">Digitando</span>
          </div>
        </div>
      </div>
    );
  }
);

TypingIndicator.displayName = "TypingIndicator";

export default TypingIndicator;
