/**
 * Sistema de logging seguro para evitar exposição de dados sensíveis
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogOptions {
  level?: LogLevel;
  sanitize?: boolean;
  context?: string;
}

class SecureLogger {
  private isDevelopment = process.env.NODE_ENV === 'development';

  /**
   * Lista de campos sensíveis que devem ser mascarados
   */
  private sensitiveFields = [
    'password',
    'token',
    'access_token',
    'refresh_token',
    'private_key',
    'secret',
    'api_key',
    'apikey',
    'authorization',
    'auth',
    'credential',
    'session_id',
    'user_id',
    'email',
    'phone',
    'cpf',
    'cnpj',
    'credit_card',
    'card_number'
  ];

  /**
   * Mascara dados sensíveis em objetos
   */
  private sanitizeData(data: any): any {
    if (typeof data === 'string') {
      // Mascarar tokens e chaves que aparecem como strings
      if (this.looksLikeToken(data)) {
        return this.maskToken(data);
      }
      return data;
    }

    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeData(item));
    }

    if (data && typeof data === 'object') {
      const sanitized: any = {};

      for (const [key, value] of Object.entries(data)) {
        const lowerKey = key.toLowerCase();

        if (this.sensitiveFields.some(field => lowerKey.includes(field))) {
          sanitized[key] = this.maskSensitiveValue(value);
        } else {
          sanitized[key] = this.sanitizeData(value);
        }
      }

      return sanitized;
    }

    return data;
  }

  /**
   * Verifica se uma string parece ser um token
   */
  private looksLikeToken(str: string): boolean {
    // JWT tokens
    if (str.match(/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/)) {
      return true;
    }

    // Bearer tokens
    if (str.startsWith('Bearer ')) {
      return true;
    }

    // Google access tokens (ya29.*)
    if (str.startsWith('ya29.')) {
      return true;
    }

    // Chaves privadas
    if (str.includes('-----BEGIN') && str.includes('-----END')) {
      return true;
    }

    // Strings longas que parecem tokens (mais de 32 chars alfanuméricos)
    if (str.length > 32 && /^[A-Za-z0-9+/=_-]+$/.test(str)) {
      return true;
    }

    return false;
  }

  /**
   * Mascara um token preservando início e fim
   */
  private maskToken(token: string): string {
    if (token.length <= 8) {
      return '***';
    }

    const start = token.substring(0, 4);
    const end = token.substring(token.length - 4);
    return `${start}***${end}`;
  }

  /**
   * Mascara valores sensíveis
   */
  private maskSensitiveValue(value: any): string {
    if (typeof value === 'string') {
      return this.maskToken(value);
    }

    if (typeof value === 'number') {
      return '***';
    }

    return '[MASKED]';
  }

  /**
   * Formata a mensagem de log com contexto
   */
  private formatMessage(message: string, context?: string): string {
    const timestamp = new Date().toISOString();
    const contextStr = context ? `[${context}] ` : '';
    return `${timestamp} ${contextStr}${message}`;
  }

  /**
   * Log de debug (apenas em desenvolvimento)
   */
  debug(message: string, data?: any, options: LogOptions = {}): void {
    if (!this.isDevelopment) {
      return;
    }

    const sanitizedData = options.sanitize !== false ? this.sanitizeData(data) : data;
    const formattedMessage = this.formatMessage(message, options.context);

    if (data) {
      console.debug(formattedMessage, sanitizedData);
    } else {
      console.debug(formattedMessage);
    }
  }

  /**
   * Log de informação
   */
  info(message: string, data?: any, options: LogOptions = {}): void {
    const sanitizedData = options.sanitize !== false ? this.sanitizeData(data) : data;
    const formattedMessage = this.formatMessage(message, options.context);

    if (data) {
      console.info(formattedMessage, sanitizedData);
    } else {
      console.info(formattedMessage);
    }
  }

  /**
   * Log de aviso
   */
  warn(message: string, data?: any, options: LogOptions = {}): void {
    const sanitizedData = options.sanitize !== false ? this.sanitizeData(data) : data;
    const formattedMessage = this.formatMessage(message, options.context);

    if (data) {
      console.warn(formattedMessage, sanitizedData);
    } else {
      console.warn(formattedMessage);
    }
  }

  /**
   * Log de erro
   */
  error(message: string, error?: any, options: LogOptions = {}): void {
    const formattedMessage = this.formatMessage(message, options.context);

    if (error) {
      // Para erros, sanitizar apenas se explicitamente solicitado
      const errorData = options.sanitize === true ? this.sanitizeData(error) : error;
      console.error(formattedMessage, errorData);
    } else {
      console.error(formattedMessage);
    }
  }

  /**
   * Log de autenticação (sempre sanitizado)
   */
  auth(message: string, data?: any, options: LogOptions = {}): void {
    const sanitizedData = this.sanitizeData(data);
    const formattedMessage = this.formatMessage(message, options.context || 'AUTH');

    if (data) {
      console.info(formattedMessage, sanitizedData);
    } else {
      console.info(formattedMessage);
    }
  }

  /**
   * Log de API (sempre sanitizado)
   */
  api(message: string, data?: any, options: LogOptions = {}): void {
    const sanitizedData = this.sanitizeData(data);
    const formattedMessage = this.formatMessage(message, options.context || 'API');

    if (data) {
      console.info(formattedMessage, sanitizedData);
    } else {
      console.info(formattedMessage);
    }
  }
}

// Singleton instance
export const secureLogger = new SecureLogger();

// Convenience exports
export const logger = secureLogger;
export default secureLogger;
