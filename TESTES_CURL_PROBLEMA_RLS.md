# 🔍 TESTES cURL PARA PROBLEMA RLS IDENTIFICADO

## 🚨 PROBLEMA IDENTIFICADO

Com base na documentação do Supabase e nos logs, o problema é:

**A função `app.get_current_tenant_id()` está tentando converter string vazia (`""`) para UUID, causando erro `22P02`**

## 📋 CONFIGURAÇÃO POSTMAN

```
SUPABASE_URL: https://opawvlqvsbltimqlzbdo.supabase.co
ANON_KEY: [sua_anon_key]
SERVICE_KEY: [sua_service_role_key]
USER_ID: 38cc3c24-7d83-4194-bcc9-68a501ceff2d
TENANT_ID: 1c11dd1a-97a8-45fd-a295-f56963f50f9a
```

## 🧪 SEQUÊNCIA DE TESTES CRÍTICOS

### **TESTE 1: Verificar se Funções RLS Existem**

**cURL:**
```bash
curl -X POST "https://opawvlqvsbltimqlzbdo.supabase.co/rest/v1/rpc/get_current_tenant_id" \
  -H "apikey: [ANON_KEY]" \
  -H "Authorization: Bearer [ANON_KEY]" \
  -H "Accept-Profile: app" \
  -H "Content-Type: application/json" \
  -d '{}'
```

**Postman:**
- **Method**: POST
- **URL**: `{{SUPABASE_URL}}/rest/v1/rpc/get_current_tenant_id`
- **Headers**:
  - `apikey`: `{{ANON_KEY}}`
  - `Authorization`: `Bearer {{ANON_KEY}}`
  - `Accept-Profile`: `app`
  - `Content-Type`: `application/json`
- **Body** (raw JSON): `{}`

**Resultado Esperado**: `null` ou erro se função não existir

---

### **TESTE 2: Definir Contexto de Tenant**

**cURL:**
```bash
curl -X POST "https://opawvlqvsbltimqlzbdo.supabase.co/rest/v1/rpc/set_current_tenant" \
  -H "apikey: [ANON_KEY]" \
  -H "Authorization: Bearer [ANON_KEY]" \
  -H "Accept-Profile: app" \
  -H "Content-Type: application/json" \
  -d '{"tenant_uuid": "1c11dd1a-97a8-45fd-a295-f56963f50f9a"}'
```

**Postman:**
- **Method**: POST
- **URL**: `{{SUPABASE_URL}}/rest/v1/rpc/set_current_tenant`
- **Headers**: (mesmos do teste 1)
- **Body** (raw JSON):
```json
{
  "tenant_uuid": "1c11dd1a-97a8-45fd-a295-f56963f50f9a"
}
```

**Resultado Esperado**: Sucesso (200) ou erro

---

### **TESTE 3: Verificar Contexto Imediatamente**

**cURL:**
```bash
curl -X POST "https://opawvlqvsbltimqlzbdo.supabase.co/rest/v1/rpc/get_current_tenant_id" \
  -H "apikey: [ANON_KEY]" \
  -H "Authorization: Bearer [ANON_KEY]" \
  -H "Accept-Profile: app" \
  -H "Content-Type: application/json" \
  -d '{}'
```

**Resultado Esperado**: UUID do tenant OU `null` (se contexto não persistir)

---

### **TESTE 4: Buscar Usuário (DEVE FALHAR)**

**cURL:**
```bash
curl -X GET "https://opawvlqvsbltimqlzbdo.supabase.co/rest/v1/users?select=tenant_id,full_name,email&user_id=eq.38cc3c24-7d83-4194-bcc9-68a501ceff2d" \
  -H "apikey: [ANON_KEY]" \
  -H "Authorization: Bearer [ANON_KEY]" \
  -H "Accept-Profile: app" \
  -H "Content-Type: application/json"
```

**Postman:**
- **Method**: GET
- **URL**: `{{SUPABASE_URL}}/rest/v1/users?select=tenant_id,full_name,email&user_id=eq.{{USER_ID}}`
- **Headers**: (mesmos dos testes anteriores)

**Resultado Esperado**: Erro `22P02: invalid input syntax for type uuid: ""`

---

### **TESTE 5: Bypass RLS (DEVE FUNCIONAR)**

**cURL:**
```bash
curl -X GET "https://opawvlqvsbltimqlzbdo.supabase.co/rest/v1/users?select=tenant_id,full_name,email&user_id=eq.38cc3c24-7d83-4194-bcc9-68a501ceff2d" \
  -H "apikey: [SERVICE_KEY]" \
  -H "Authorization: Bearer [SERVICE_KEY]" \
  -H "Accept-Profile: app" \
  -H "Content-Type: application/json"
```

**Postman:**
- **Method**: GET
- **URL**: `{{SUPABASE_URL}}/rest/v1/users?select=tenant_id,full_name,email&user_id=eq.{{USER_ID}}`
- **Headers**:
  - `apikey`: `{{SERVICE_KEY}}`
  - `Authorization`: `Bearer {{SERVICE_KEY}}`
  - `Accept-Profile`: `app`
  - `Content-Type`: `application/json`

**Resultado Esperado**: Dados do usuário (deve funcionar)

---

### **TESTE 6: Verificar Tenants**

**cURL:**
```bash
curl -X GET "https://opawvlqvsbltimqlzbdo.supabase.co/rest/v1/tenants?select=id,subdomain,name&subdomain=eq.vasco" \
  -H "apikey: [ANON_KEY]" \
  -H "Authorization: Bearer [ANON_KEY]" \
  -H "Accept-Profile: app" \
  -H "Content-Type: application/json"
```

**Resultado Esperado**: Dados do tenant Vasco

---

## 🔍 DIAGNÓSTICO ESPERADO

### **Se o problema for contexto HTTP:**
- Teste 2: Sucesso (contexto definido)
- Teste 3: `null` (contexto perdido)
- Teste 4: Erro 22P02 (string vazia)

### **Se o problema for função RLS:**
- Teste 1: Erro (função não existe)
- Teste 2: Erro (função não existe)

### **Se o problema for política RLS:**
- Testes 1-3: Funcionam
- Teste 4: Erro 22P02 (política mal configurada)

## 🛠️ CORREÇÃO BASEADA NO DIAGNÓSTICO

### **Problema 1: Contexto não persiste (HTTP)**
```sql
-- Usar configuração de sessão em vez de transação
CREATE OR REPLACE FUNCTION app.set_current_tenant(tenant_uuid UUID)
RETURNS void AS $$
BEGIN
  PERFORM set_config('app.current_tenant_id', tenant_uuid::text, false); -- false = sessão
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### **Problema 2: Função não trata string vazia**
```sql
-- Função que trata string vazia corretamente
CREATE OR REPLACE FUNCTION app.get_current_tenant_id()
RETURNS UUID AS $$
DECLARE
    tenant_setting TEXT;
BEGIN
    tenant_setting := current_setting('app.current_tenant_id', true);
    
    IF tenant_setting IS NULL OR tenant_setting = '' THEN
        RETURN NULL;
    END IF;
    
    RETURN tenant_setting::UUID;
EXCEPTION WHEN OTHERS THEN
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### **Problema 3: Política RLS mal configurada**
```sql
-- Política que trata NULL corretamente
CREATE POLICY tenant_isolation_users ON app.users
  FOR ALL USING (
    tenant_id = app.get_current_tenant_id() 
    AND app.get_current_tenant_id() IS NOT NULL
  );
```

## 🎯 PRÓXIMOS PASSOS

1. **Execute os testes no Postman na ordem**
2. **Execute o script SQL**: `scripts/investigacao-rls-profunda.sql`
3. **Compare resultados**: Identifique onde falha
4. **Aplique correção específica** baseada no diagnóstico

---

**Execute os testes e me reporte os resultados! Vamos identificar e corrigir o problema RLS. 🔍**
