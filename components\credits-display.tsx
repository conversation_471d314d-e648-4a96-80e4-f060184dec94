'use client';

import { Badge } from '@/components/ui/badge';
import { Coins, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useCreditsContext } from '@/components/providers/credits-provider';
import React, { useState } from 'react';

interface CreditsDisplayProps {
  onCreditsUpdate?: (credits: number) => void;
}

export default function CreditsDisplay({ onCreditsUpdate }: CreditsDisplayProps) {
  const { credits, loading, refreshCredits } = useCreditsContext();
  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = async () => {
    setRefreshing(true);
    await refreshCredits();
    setRefreshing(false);
  };

  // Notificar mudanças de créditos para componente pai
  React.useEffect(() => {
    onCreditsUpdate?.(credits);
  }, [credits, onCreditsUpdate]);

  if (loading) {
    return (
      <div className="flex items-center gap-2">
        <RefreshCw className="h-4 w-4 animate-spin" />
        <span className="text-sm text-muted-foreground">Carregando...</span>
      </div>
    );
  }

  const getCreditsColor = () => {
    if (credits === 0) return 'destructive';
    if (credits <= 5) return 'secondary';
    return 'default';
  };

  const getCreditsIcon = () => {
    if (credits === 0) return '🚫';
    if (credits <= 5) return '⚠️';
    return '💳';
  };

  return (
    <div className="flex items-center gap-2">
      <Badge 
        variant={getCreditsColor()} 
        className="flex items-center gap-1 px-2 py-1"
      >
        <span className="text-xs">{getCreditsIcon()}</span>
        <Coins className="h-3 w-3" />
        <span className="font-medium">{credits}</span>
        <span className="text-xs opacity-75">créditos</span>
      </Badge>
      
      <Button
        variant="ghost"
        size="sm"
        onClick={handleRefresh}
        disabled={refreshing}
        className="h-6 w-6 p-0"
      >
        <RefreshCw className={`h-3 w-3 ${refreshing ? 'animate-spin' : ''}`} />
      </Button>
    </div>
  );
}
