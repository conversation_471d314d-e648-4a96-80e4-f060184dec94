import { createClient } from '@supabase/supabase-js';
import { creditsService } from './services/credits-service';
import { pLogger } from './utils/production-logger';

// Configuração de preços por token (em USD por 1 milhão de tokens)
const TOKEN_COSTS = {
  INPUT: 0.15 / 1_000_000, // US$ 0.15 por 1M tokens de entrada
  CANDIDATES: 0.60 / 1_000_000, // US$ 0.60 por 1M tokens de saída
  THOUGHTS: 3.50 / 1_000_000, // US$ 3.50 por 1M tokens de pensamento
} as const;

interface AgentResponse {
  content: {
    parts: any[];
    role: string;
  };
  usage_metadata?: {
    candidates_token_count: number;
    prompt_token_count: number;
    thoughts_token_count: number;
    total_token_count: number;
    traffic_type: string;
  };
  invocation_id: string;
  author: string;
  id: string;
  timestamp: number;
}

interface CostLogEntry {
  invocation_id: string;
  user_id: string;
  session_id: string;
  response_timestamp: string;
  agent_author: string;
  prompt_token_count: number;
  candidates_token_count: number;
  thoughts_token_count: number;
  input_cost_usd: number;
  thoughts_cost_usd: number;
  candidates_cost_usd: number;
  total_cost_usd: number;
  response_text: string;
  metadata: any;
  user_credits_after: number; // Créditos do usuário após o desconto
}

export class AgentCostLogger {
  private supabase;

  constructor() {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      pLogger.warn('Supabase não configurado - logging de custos desabilitado', {
        urlPresent: !!supabaseUrl,
        keyPresent: !!supabaseKey
      }, { context: 'COST' });
      this.supabase = null;
      return;
    }

    this.supabase = createClient(supabaseUrl, supabaseKey);
  }

  /**
   * Calcula os custos baseado nos tokens utilizados
   */
  private calculateCosts(usage_metadata: AgentResponse['usage_metadata']) {
    if (!usage_metadata) {
      return {
        input_cost_usd: 0,
        thoughts_cost_usd: 0,
        candidates_cost_usd: 0,
        total_cost_usd: 0,
      };
    }

    // Garantir que os valores sejam números válidos
    const promptTokens = Number(usage_metadata.prompt_token_count) || 0;
    const thoughtsTokens = Number(usage_metadata.thoughts_token_count) || 0;
    const candidatesTokens = Number(usage_metadata.candidates_token_count) || 0;

    const input_cost_usd = promptTokens * TOKEN_COSTS.INPUT;
    const thoughts_cost_usd = thoughtsTokens * TOKEN_COSTS.THOUGHTS;
    const candidates_cost_usd = candidatesTokens * TOKEN_COSTS.CANDIDATES;
    const total_cost_usd = input_cost_usd + thoughts_cost_usd + candidates_cost_usd;

    return {
      input_cost_usd: Number(input_cost_usd.toFixed(10)),
      thoughts_cost_usd: Number(thoughts_cost_usd.toFixed(10)),
      candidates_cost_usd: Number(candidates_cost_usd.toFixed(10)),
      total_cost_usd: Number(total_cost_usd.toFixed(10)),
    };
  }

  /**
   * Extrai o texto da resposta do agente
   */
  private extractResponseText(response: AgentResponse): string {
    try {
      if (!response.content?.parts || response.content.parts.length === 0) {
        return '';
      }

      let text = '';
      for (const part of response.content.parts) {
        if (part.text) {
          text += part.text + ' ';
        }
      }

      return text.trim();
    } catch (error) {
      console.error('Erro ao extrair texto da resposta:', error);
      return '';
    }
  }

  /**
   * Registra o custo de uma resposta do agente (SEM consumir créditos)
   * Os créditos são consumidos apenas uma vez por invocação
   */
  async logAgentResponse(
    response: AgentResponse,
    userId: string,
    sessionId: string,
    userCreditsAfter?: number
  ): Promise<{ success: boolean; remainingCredits: number; error?: string }> {
    try {
      console.log('🔄 AgentCostLogger.logAgentResponse chamado');

      // Verificar se Supabase está configurado
      if (!this.supabase) {
        console.log('❌ Supabase não configurado, pulando log');
        return { success: false, remainingCredits: userCreditsAfter || 0, error: 'Supabase não configurado' };
      }

      console.log('✅ Supabase configurado');

      // Só registra respostas do modelo (não do usuário)
      if (response.content?.role !== 'model') {
        console.log('⏭️ Resposta não é do modelo, pulando log');
        return {
          success: true,
          remainingCredits: userCreditsAfter || 0
        };
      }

      console.log('✅ Resposta é do modelo');

      // Só registra se tiver usage_metadata (custos)
      if (!response.usage_metadata) {
        console.log('⚠️ Resposta sem usage_metadata, pulando log de custo');
        return {
          success: true,
          remainingCredits: userCreditsAfter || 0
        };
      }

      console.log('✅ Resposta tem usage_metadata:', response.usage_metadata);

      const costs = this.calculateCosts(response.usage_metadata);
      const responseText = this.extractResponseText(response);

      console.log('💵 Custos calculados:', costs);

      const logEntry: CostLogEntry = {
        invocation_id: response.invocation_id,
        user_id: userId,
        session_id: sessionId,
        response_timestamp: new Date(response.timestamp * 1000).toISOString(),
        agent_author: response.author,
        prompt_token_count: response.usage_metadata.prompt_token_count || 0,
        candidates_token_count: response.usage_metadata.candidates_token_count || 0,
        thoughts_token_count: response.usage_metadata.thoughts_token_count || 0,
        ...costs,
        response_text: responseText,
        metadata: response,
        user_credits_after: userCreditsAfter || 0,
      };

      pLogger.cost('Entrada de log preparada', {
        agent_author: logEntry.agent_author,
        total_cost_usd: logEntry.total_cost_usd,
        prompt_tokens: logEntry.prompt_token_count,
        candidates_tokens: logEntry.candidates_token_count,
        thoughts_tokens: logEntry.thoughts_token_count
        // IDs de usuário e sessão removidos dos logs por segurança
      });

      pLogger.cost('Tentando inserir no Supabase...');
      // URLs e chaves removidas dos logs por segurança

      // Teste básico de conectividade
      try {
        pLogger.cost('Testando conectividade com Supabase...');
        const { error: testError } = await this.supabase
          .schema('app')
          .from('agent_interaction_costs')
          .select('count(*)', { count: 'exact', head: true });

        if (testError) {
          pLogger.cost('Teste de conectividade falhou', { errorCode: testError.code });
        } else {
          pLogger.cost('Conectividade OK');
        }
      } catch (testErr) {
        pLogger.cost('Erro no teste de conectividade', testErr);
      }

      try {
        // Usar o método .schema() para acessar o schema 'app'
        pLogger.cost('Tentando inserção no schema app...');
        const { data, error } = await this.supabase
          .schema('app')
          .from('agent_interaction_costs')
          .insert([logEntry])
          .select();

        if (error) {
          pLogger.error('Erro ao salvar log de custo', {
            code: error.code,
            message: error.message
            // Detalhes completos removidos por segurança
          }, { context: 'COST' });

          return {
            success: false,
            remainingCredits: userCreditsAfter || 0,
            error: 'Erro ao salvar log'
          };
        } else {
          pLogger.cost(`Custo registrado com sucesso: $${costs.total_cost_usd.toFixed(6)} para ${response.author}`);
          pLogger.cost(`Créditos restantes: ${userCreditsAfter || 0}`);

          return {
            success: true,
            remainingCredits: userCreditsAfter || 0
          };
        }
      } catch (networkError: any) {
        console.error('❌ Erro de rede/conexão:', networkError);
        console.error('📋 Stack trace:', networkError?.stack);

        return {
          success: false,
          remainingCredits: userCreditsAfter || 0,
          error: 'Erro de rede'
        };
      }
    } catch (error) {
      console.error('Erro no AgentCostLogger:', error);
      return {
        success: false,
        remainingCredits: 0,
        error: 'Erro interno'
      };
    }
  }

  /**
   * Registra múltiplas respostas de uma invocação (NOVA IMPLEMENTAÇÃO)
   * Consome apenas 1 crédito por invocação, independente do número de agentes
   */
  async logInvocationResponses(
    responses: AgentResponse[],
    userId: string,
    sessionId: string
  ): Promise<{ success: boolean; remainingCredits: number; error?: string }> {
    try {
      console.log('🔄 AgentCostLogger.logInvocationResponses chamado');
      console.log(`📊 Total de respostas a processar: ${responses.length}`);

      // Filtrar apenas respostas válidas para logging
      const validResponses = responses.filter(response =>
        response.usage_metadata &&
        response.content?.role === 'model' &&
        response.invocation_id &&
        response.author
      );

      console.log(`💰 Respostas válidas para logging: ${validResponses.length} de ${responses.length}`);

      if (validResponses.length === 0) {
        console.log('⚠️ Nenhuma resposta válida encontrada para logging de custos');
        return { success: true, remainingCredits: 0 };
      }

      // Verificar se há pelo menos uma resposta com sucesso (texto extraído)
      const hasSuccessfulResponse = validResponses.some(response => {
        const responseText = this.extractResponseText(response);
        return responseText && responseText.trim().length > 0;
      });

      if (!hasSuccessfulResponse) {
        console.log('⚠️ Nenhuma resposta com conteúdo válido encontrada');
        return { success: true, remainingCredits: 0 };
      }

      // Consumir apenas 1 crédito por invocação (usando o primeiro invocation_id)
      const invocationId = validResponses[0].invocation_id;
      console.log(`💳 Consumindo 1 crédito para invocação: ${invocationId}`);

      const creditsResult = await creditsService.consumeCredits(userId, 1, invocationId);

      if (!creditsResult.success) {
        console.log('❌ Falha ao consumir créditos:', creditsResult.error);
        return creditsResult;
      }

      console.log(`✅ Crédito consumido com sucesso. Restantes: ${creditsResult.remainingCredits}`);

      // Agora fazer o logging de todas as respostas válidas com o mesmo valor de créditos
      let allLogsSuccessful = true;
      let lastError = '';

      for (const response of validResponses) {
        console.log('💾 Fazendo log da resposta:', {
          invocation_id: response.invocation_id,
          author: response.author,
          userId,
          sessionId
        });

        const logResult = await this.logAgentResponse(
          response,
          userId,
          sessionId,
          creditsResult.remainingCredits
        );

        if (!logResult.success) {
          console.error('❌ Falha no log da resposta:', logResult.error);
          allLogsSuccessful = false;
          lastError = logResult.error || 'Erro desconhecido';
        }
      }

      if (!allLogsSuccessful) {
        return {
          success: false,
          remainingCredits: creditsResult.remainingCredits,
          error: `Alguns logs falharam: ${lastError}`
        };
      }

      return {
        success: true,
        remainingCredits: creditsResult.remainingCredits
      };

    } catch (error) {
      console.error('❌ Erro no logInvocationResponses:', error);
      return {
        success: false,
        remainingCredits: 0,
        error: 'Erro interno'
      };
    }
  }

  /**
   * Busca custos por usuário em um período
   */
  async getUserCosts(
    userId: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<{ total_cost: number; interactions: number }> {
    try {
      if (!this.supabase) {
        return { total_cost: 0, interactions: 0 };
      }

      let query = this.supabase
        .schema('app')
        .from('agent_interaction_costs')
        .select('total_cost_usd')
        .eq('user_id', userId);

      if (startDate) {
        query = query.gte('response_timestamp', startDate.toISOString());
      }
      if (endDate) {
        query = query.lte('response_timestamp', endDate.toISOString());
      }

      const { data, error } = await query;

      if (error) {
        console.error('Erro ao buscar custos do usuário:', error);
        return { total_cost: 0, interactions: 0 };
      }

      const total_cost = data?.reduce((sum, item) => sum + Number(item.total_cost_usd), 0) || 0;
      const interactions = data?.length || 0;

      return { total_cost, interactions };
    } catch (error) {
      console.error('Erro ao calcular custos do usuário:', error);
      return { total_cost: 0, interactions: 0 };
    }
  }

  /**
   * Busca custos por invocation_id
   */
  async getInvocationCosts(invocationId: string) {
    try {
      if (!this.supabase) {
        return null;
      }

      const { data, error } = await this.supabase
        .schema('app')
        .from('agent_interaction_costs')
        .select('*')
        .eq('invocation_id', invocationId)
        .order('response_timestamp', { ascending: true });

      if (error) {
        console.error('Erro ao buscar custos da invocação:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Erro ao buscar custos da invocação:', error);
      return null;
    }
  }
}

// Instância singleton
export const agentCostLogger = new AgentCostLogger();
