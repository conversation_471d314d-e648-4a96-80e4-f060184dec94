import { createServerClient } from "@supabase/ssr";
import { cookies, headers } from "next/headers";

export const createClient = async () => {
  const cookieStore = await cookies();
  const headersList = await headers();
  const tenantId = headersList.get('x-tenant-id');

  const client = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          } catch (error) {
            // The `set` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    },
  );

  // CRÍTICO: Definir tenant atual para RLS
  if (tenantId) {
    try {
      await client.schema('app').rpc('set_current_tenant', { tenant_uuid: tenantId });
    } catch (error) {
      console.error('Erro ao definir tenant no contexto RLS:', error);
    }
  }

  return client;
};
