import { NextRequest, NextResponse } from 'next/server';
import { agentCostLogger } from '@/lib/agent-cost-logger';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('user_id');
    const invocationId = searchParams.get('invocation_id');
    const startDate = searchParams.get('start_date');
    const endDate = searchParams.get('end_date');

    if (invocationId) {
      // Buscar custos por invocation_id
      const costs = await agentCostLogger.getInvocationCosts(invocationId);
      return NextResponse.json(costs);
    }

    if (userId) {
      // Buscar custos por usuário
      const start = startDate ? new Date(startDate) : undefined;
      const end = endDate ? new Date(endDate) : undefined;
      
      const costs = await agentCostLogger.getUserCosts(userId, start, end);
      return NextResponse.json(costs);
    }

    return NextResponse.json(
      { error: 'Parâmetro user_id ou invocation_id é obrigatório' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Erro ao buscar custos:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
