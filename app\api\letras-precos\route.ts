// app/api/letras/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

// Definindo os headers CORS como constante (não exportada)
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, apikey'
};

export async function OPTIONS() {
  return NextResponse.json({}, { headers: corsHeaders });
}

export async function GET(request: NextRequest) {
  // Verificar autenticação
  const authHeader = request.headers.get('Authorization');

  if (!authHeader) {
    return NextResponse.json(
      { error: 'Cabeçalho de autorização não fornecido' },
      { status: 401, headers: corsHeaders }
    );
  }

  const token = authHeader.replace('Bearer ', '');
  const supabase = await createClient();

  // Verificar o usuário
  const { data: { user }, error: userError } = await supabase.auth.getUser(token);

  if (userError || !user) {
    return NextResponse.json(
      { error: 'Token inválido' },
      { status: 401, headers: corsHeaders }
    );
  }

  // Obter o parâmetro data_referencia, se fornecido
  const url = new URL(request.url);
  const dataReferencia = url.searchParams.get('data_referencia');

  // SEGURANÇA: Construir a consulta usando schema correto (RLS aplicado automaticamente)
  let query = supabase
    .from('letras_precos')
    .select('cnpj, data_referencia, doze_meses, emissor, fluxo, indexador, letra, oitenta_e_quatro_meses, quarenta_e_oito_meses, seis_meses, sessenta_meses, setenta_e_dois_meses, taxa, tres_meses, trinta_seis_meses, umes, vinte_quatro_meses');

  // Adicionar filtro por data_referencia, se fornecido
  if (dataReferencia) {
    query = query.eq('data_referencia', dataReferencia);
  }

  const { data, error } = await query;

  if (error) {
    return NextResponse.json(
      { error: error.message },
      { status: 500, headers: corsHeaders }
    );
  }

  return NextResponse.json(
    { data },
    { headers: corsHeaders }
  );
}