"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { toast } from "sonner";
import { signInClient } from "@/app/client-actions";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { FormMessage, Message } from "@/components/form-message";
import { AppConfig } from "@/lib/config/app-config";

export default function LoginFormClient({ initialMessage }: { initialMessage?: Message }) {
  const router = useRouter();
  const [message, setMessage] = useState<Message | null>(initialMessage || null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Verificar se signup está habilitado
  const isSignupEnabled = AppConfig.enableSignup;

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsSubmitting(true);

    // Toast de início do processo de login
    toast.loading("Verificando credenciais...", {
      id: "login-process"
    });

    try {
      const formData = new FormData(event.currentTarget);
      const result = await signInClient(formData);

      if (result.success) {
        // Dismiss loading toast e mostrar sucesso
        toast.success("Login realizado com sucesso!", {
          id: "login-process",
          description: "Redirecionando para o chat...",
          duration: 2000,
        });

        // Redirecionar para o chat
        router.push("/chat");
      }
    } catch (error: any) {
      const errorMessage = error.message || "Falha ao fazer login";

      // Definir mensagem de erro mais específica
      let userFriendlyMessage = errorMessage;
      if (errorMessage.includes("Invalid login credentials")) {
        userFriendlyMessage = "Email ou senha incorretos";
      } else if (errorMessage.includes("Email not confirmed")) {
        userFriendlyMessage = "Verifique seu email para confirmar a conta";
      } else if (errorMessage.includes("Too many requests")) {
        userFriendlyMessage = "Muitas tentativas. Tente novamente em alguns minutos";
      }

      // Dismiss loading toast e mostrar erro
      toast.error("Erro no login", {
        id: "login-process",
        description: userFriendlyMessage,
        duration: 4000,
        action: {
          label: "Tentar novamente",
          onClick: () => {
            // Limpar mensagem de erro para permitir nova tentativa
            setMessage(null);
          }
        }
      });

      // Manter também a mensagem de erro no formulário
      setMessage({
        error: userFriendlyMessage
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form className="w-full flex flex-col" onSubmit={handleSubmit} suppressHydrationWarning>
      <h1 className="text-2xl font-medium text-center mb-2">Entrar</h1>
      {isSignupEnabled && (
        <p className="text-sm text-foreground text-center mb-8">
          Não tem uma conta?{" "}
          <Link className="text-primary font-medium underline" href="/sign-up">
            Criar conta
          </Link>
        </p>
      )}
      <div className="flex flex-col gap-4" suppressHydrationWarning>
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input name="email" placeholder="<EMAIL>" required suppressHydrationWarning />
        </div>
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <Label htmlFor="password">Senha</Label>
            <Link
              className="text-xs text-foreground underline"
              href="/forgot-password"
            >
              Esqueceu a senha?
            </Link>
          </div>
          <Input
            type="password"
            name="password"
            placeholder="Sua senha"
            required
            suppressHydrationWarning
          />
        </div>
        <div className="pt-4">
          <SubmitButton
            className="w-full"
            pendingText="Entrando..."
            disabled={isSubmitting}
          >
            Entrar
          </SubmitButton>
        </div>
        {message && <FormMessage message={message} />}
      </div>
    </form>
  );
}
