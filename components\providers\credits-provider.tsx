'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import { useCredits } from '@/components/hooks/use-credits';

interface CreditsContextType {
  credits: number;
  loading: boolean;
  updateCredits: (newCredits: number) => void;
  refreshCredits: () => Promise<void>;
  decrementCredits: () => void;
}

const CreditsContext = createContext<CreditsContextType | undefined>(undefined);

interface CreditsProviderProps {
  children: ReactNode;
  userId: string;
}

export function CreditsProvider({ children, userId }: CreditsProviderProps) {
  const creditsData = useCredits(userId);

  return (
    <CreditsContext.Provider value={creditsData}>
      {children}
    </CreditsContext.Provider>
  );
}

export function useCreditsContext() {
  const context = useContext(CreditsContext);
  if (context === undefined) {
    throw new Error('useCreditsContext must be used within a CreditsProvider');
  }
  return context;
}
