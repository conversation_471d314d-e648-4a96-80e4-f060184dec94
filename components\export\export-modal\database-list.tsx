"use client";

import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { But<PERSON> } from "@/components/ui/button";
import { Database } from "@/types/database";
import { InfoIcon } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface DatabaseListProps {
  databases: Database[];
  selectedDatabases: string[];
  toggleDatabase: (id: string) => void;
  toggleSelectAll: () => void;
  allSelected: boolean;
}

export function DatabaseList({ 
  databases, 
  selectedDatabases, 
  toggleDatabase, 
  toggleSelectAll,
  allSelected
}: DatabaseListProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between border-b pb-2">
        <span className="font-medium">Bases de Dados</span>
        <Button 
          variant="ghost" 
          size="sm"
          onClick={toggleSelectAll}
          className="text-xs"
        >
          {allSelected ? "Desmarcar Todas" : "Selecionar Todas"}
        </Button>
      </div>
      
      <div className="grid gap-3 max-h-[240px] overflow-y-auto pt-1 pr-1">
        {databases.map((database) => (
          <div 
            key={database.id} 
            className="flex items-center justify-between space-x-2 p-2 rounded-md hover:bg-accent/50"
          >
            <div className="flex items-center space-x-2">
              <Checkbox
                id={database.id}
                checked={selectedDatabases.includes(database.id)}
                onCheckedChange={() => toggleDatabase(database.id)}
              />
              <Label
                htmlFor={database.id}
                className="cursor-pointer"
              >
                {database.name}
              </Label>
            </div>
            
            {database.description && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <InfoIcon className="h-4 w-4 text-muted-foreground cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-xs text-sm">{database.description}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
        ))}
      </div>
    </div>
  );
} 