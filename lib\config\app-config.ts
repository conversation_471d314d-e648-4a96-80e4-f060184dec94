/**
 * Configurações da aplicação baseadas em variáveis de ambiente
 */

export const AppConfig = {
  /**
   * Controla se o registro de novos usuários está habilitado
   * @default true
   */
  enableSignup: process.env.NEXT_PUBLIC_ENABLE_SIGNUP === 'true',

  /**
   * Limite máximo de caracteres por mensagem no chat
   * @default 5000
   */
  chatMessageMaxChars: parseInt(process.env.CHAT_MESSAGE_MAX_CHARS || '5000'),

  /**
   * Controla se o histórico de conversas (session sidebar) está habilitado
   * @default true
   */
  enableSessionHistory: process.env.NEXT_PUBLIC_ENABLE_SESSION_HISTORY !== 'false',

  /**
   * Ambiente do chat (local ou production)
   */
  chatEnvironment: process.env.CHAT_ENVIRONMENT || 'local',

  /**
   * URLs e configurações do servidor local
   */
  localServer: {
    url: process.env.LOCAL_AI_SERVER_URL || 'http://localhost:8000',
    agentName: process.env.LOCAL_AGENT_NAME || 'financial-agent'
  },

  /**
   * Configurações do Supabase
   */
  supabase: {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
    anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || ''
  },

  /**
   * Configurações do Google Cloud
   */
  googleCloud: {
    projectId: process.env.GOOGLE_CLOUD_PROJECT_ID || '',
    credentials: process.env.GOOGLE_APPLICATION_CREDENTIALS || ''
  },

  /**
   * Configurações do indicador de digitação com contagem regressiva
   */
  typingIndicator: {
    /**
     * Duração total da animação de contagem regressiva em segundos
     * @default 30
     */
    duration: parseInt(process.env.TYPING_INDICATOR_DURATION || '30'),

    /**
     * Habilita/desabilita a animação de contagem regressiva
     * @default true
     */
    enableCountdown: process.env.TYPING_INDICATOR_COUNTDOWN !== 'false',

    /**
     * Mensagens progressivas durante a contagem regressiva
     */
    progressMessages: [
      { timeRange: [0, 3], message: "Processando sua solicitação..." },
      { timeRange: [3, 7], message: "Interpretando sua pergunta..." },
      { timeRange: [7, 12], message: "Raciocinando..." },
      { timeRange: [12, 16], message: "Elaborando resposta..." },
      { timeRange: [16, 19], message: "Consultando nossas bases..." },
      { timeRange: [19, 24], message: "Revisando..." },
      { timeRange: [24, 30], message: "Estamos quase lá..." },
      { timeRange: [30, 40], message: "Só mais um pouquinho..." },
      { timeRange: [50, 60], message: "Revisando últimos detalhes da pergunta..." },
      { timeRange: [60, 70], message: "Essa pergunta está dando um pouquinho mais de trabalho..." },
      { timeRange: [70, 999], message: "Consolidando resposta..." },
    ]
  },

  /**
   * Configurações de logging e debug
   */
  logging: {
    /**
     * Habilita logs de debug em produção (PERIGOSO - apenas para troubleshooting)
     * @default false
     */
    enableProductionLogs: process.env.ENABLE_PRODUCTION_LOGS === 'true',

    /**
     * Habilita logs detalhados de API
     * @default false em produção
     */
    enableApiLogs: process.env.NODE_ENV === 'development' || process.env.ENABLE_API_LOGS === 'true',

    /**
     * Habilita logs de ECharts
     * @default false em produção
     */
    enableChartsLogs: process.env.NODE_ENV === 'development' || process.env.ENABLE_CHARTS_LOGS === 'true',

    /**
     * Habilita logs de agent cost logger
     * @default false em produção
     */
    enableCostLogs: process.env.NODE_ENV === 'development' || process.env.ENABLE_COST_LOGS === 'true',

    /**
     * Habilita logs de chat interface
     * @default false em produção
     */
    enableChatLogs: process.env.NODE_ENV === 'development' || process.env.ENABLE_CHAT_LOGS === 'true'
  }
} as const;

/**
 * Validação das configurações obrigatórias
 */
export function validateAppConfig() {
  const errors: string[] = [];

  if (!AppConfig.supabase.url) {
    errors.push('NEXT_PUBLIC_SUPABASE_URL é obrigatório');
  }

  if (!AppConfig.supabase.anonKey) {
    errors.push('NEXT_PUBLIC_SUPABASE_ANON_KEY é obrigatório');
  }

  if (AppConfig.chatEnvironment === 'production') {
    if (!AppConfig.googleCloud.projectId) {
      errors.push('GOOGLE_CLOUD_PROJECT_ID é obrigatório para ambiente de produção');
    }
  }

  if (AppConfig.chatMessageMaxChars < 1 || AppConfig.chatMessageMaxChars > 50000) {
    errors.push('CHAT_MESSAGE_MAX_CHARS deve estar entre 1 e 50000');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Utilitários para formatação
 */
export const AppUtils = {
  /**
   * Formata número com separadores brasileiros
   */
  formatNumber: (num: number) => num.toLocaleString('pt-BR'),

  /**
   * Verifica se uma string excede o limite de caracteres
   */
  isMessageTooLong: (message: string) => message.length > AppConfig.chatMessageMaxChars,

  /**
   * Gera um ID único para mensagens
   */
  generateUniqueId: (prefix: string = 'msg') =>
    `${prefix}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,

  /**
   * Retorna mensagem de erro para limite de caracteres
   */
  getCharLimitErrorMessage: (messageLength: number) =>
    `❌ **Mensagem muito longa**\n\nSua mensagem tem **${AppUtils.formatNumber(messageLength)} caracteres**.\n\nO limite máximo é de **${AppUtils.formatNumber(AppConfig.chatMessageMaxChars)} caracteres** por mensagem.\n\nPor favor, reduza o tamanho da mensagem e tente novamente.`
} as const;
