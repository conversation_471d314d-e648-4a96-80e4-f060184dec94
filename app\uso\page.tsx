'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';
import UsoStats from '@/components/uso/UsoStats';
import UsoFilters from '@/components/uso/UsoFilters';
import TokensOnlyChart from '@/components/uso/TokensOnlyChart';
import MessagesChart from '@/components/uso/MessagesChart';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CreditCard, TrendingUp, AlertCircle, MessageSquare } from 'lucide-react';
import { pLogger } from '@/lib/utils/production-logger';

interface UsoData {
  chartData: Array<{
    date: string;
    total_tokens: number;
    total_messages: number;
  }>;
  totals: {
    total_tokens: number;
    total_messages: number;
    total_invocations: number;
  };
}

export default function UsoPage() {
  const [user, setUser] = useState<any>(null);
  const [usoData, setUsoData] = useState<UsoData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    startDate: '',
    endDate: '',
    groupBy: 'day'
  });

  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    checkUser();
  }, []);

  useEffect(() => {
    if (user && filters.startDate && filters.endDate) {
      fetchUsoData();
    }
  }, [user, filters]);

  const checkUser = async () => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        console.error('Erro ao verificar sessão:', error);
        router.push('/sign-in');
        return;
      }

      if (!session) {
        router.push('/sign-in');
        return;
      }
      setUser(session.user);
      
      // Definir filtros padrão (mês atual)
      const now = new Date();
      const start = new Date(now.getFullYear(), now.getMonth(), 1); // Primeiro dia do mês atual
      const end = new Date(now.getFullYear(), now.getMonth() + 1, 0); // Último dia do mês atual
      
      const defaultFilters = {
        startDate: start.toISOString().split('T')[0],
        endDate: end.toISOString().split('T')[0],
        groupBy: 'day'
      };
      
      setFilters(defaultFilters);
      
      // Carregar dados automaticamente com filtros padrão
      fetchUsoData(defaultFilters);
    } catch (error) {
      pLogger.error('Erro ao verificar usuário', error, { context: 'AUTH' });
      setError('Erro ao verificar autenticação');
      setLoading(false);
    }
  };

  const fetchUsoData = async (customFilters?: typeof filters) => {
    if (!user) return;

    setLoading(true);
    setError(null);

    const filtersToUse = customFilters || filters;

    try {
      const params = new URLSearchParams({
        user_id: user.id,
        start_date: filtersToUse.startDate,
        end_date: filtersToUse.endDate,
        group_by: filtersToUse.groupBy
      });

      pLogger.debug('Fazendo requisição para API de uso', {
        url: `/api/uso?${params}`,
        // userId removido por segurança
        filters: filtersToUse
      }, { context: 'API' });

      const response = await fetch(`/api/uso?${params}`);

      if (!response.ok) {
        const errorText = await response.text();
        pLogger.error('Erro na resposta da API de uso', {
          status: response.status
          // errorText removido por segurança
        }, { context: 'API' });
        throw new Error('Erro ao buscar dados de Uso');
      }

      const data = await response.json();
      pLogger.debug('Dados recebidos da API de uso', {
        hasChartData: !!data.chartData,
        chartDataLength: data.chartData?.length || 0,
        hasTotals: !!data.totals
        // Dados sensíveis removidos por segurança
      }, { context: 'API' });
      setUsoData(data);
    } catch (error) {
      pLogger.error('Erro ao buscar dados de Uso', error, { context: 'API' });
      setError('Erro ao carregar dados de Uso');
    } finally {
      setLoading(false);
    }
  };

  const handleFiltersChange = (newFilters: typeof filters) => {
    setFilters(newFilters);
  };

  if (loading && !usoData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">Carregando dados de Uso...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2 flex items-center gap-2">
          <CreditCard className="h-8 w-8" />
          Histórico de uso
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Acompanhe seu uso de tokens e mensagens ao longo do tempo
        </p>
      </div>

      <UsoFilters 
        onFiltersChange={handleFiltersChange}
        loading={loading}
        initialFilters={filters}
      />

      {usoData && (
        <>
          <UsoStats totals={usoData.totals} />

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Mensagens
                </CardTitle>
              </CardHeader>
              <CardContent>
                <MessagesChart
                  data={usoData.chartData}
                  groupBy={filters.groupBy}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Tokens
                </CardTitle>
              </CardHeader>
              <CardContent>
                <TokensOnlyChart
                  data={usoData.chartData}
                  groupBy={filters.groupBy}
                />
              </CardContent>
            </Card>
          </div>


        </>
      )}
    </div>
  );
}
