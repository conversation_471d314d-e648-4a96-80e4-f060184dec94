"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>ircle, MessageSquare, Sparkles, TrendingUp } from "lucide-react";

interface NewConversationScreenProps {
  onCreateNewChat: () => void;
  isCreatingSession: boolean;
}

export default function NewConversationScreen({ 
  onCreateNewChat, 
  isCreatingSession 
}: NewConversationScreenProps) {
  return (
    <div className="flex flex-col items-center justify-center h-full w-full p-8 text-center">
      <div className="max-w-md space-y-6">
        {/* Ícone principal */}
        <div className="flex justify-center">
          <div className="w-20 h-20 rounded-full bg-primary/10 flex items-center justify-center">
            <MessageSquare className="w-10 h-10 text-primary" />
          </div>
        </div>

        {/* Título e descrição */}
        <div className="space-y-3">
          <h2 className="text-2xl font-semibold text-foreground">
            Bem-vindo ao PrimeAI
          </h2>
          <p className="text-muted-foreground leading-relaxed">
            Inicie uma nova conversa para começar a analisar dados financeiros 
            com inteligência artificial avançada.
          </p>
        </div>

        {/* Recursos disponíveis */}
        <div className="grid grid-cols-1 gap-3 text-sm">
          <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
            <TrendingUp className="w-5 h-5 text-green-500 flex-shrink-0" />
            <span className="text-left">Análise de tendências do mercado</span>
          </div>
          <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
            <Sparkles className="w-5 h-5 text-blue-500 flex-shrink-0" />
            <span className="text-left">Gráficos interativos em tempo real</span>
          </div>
          <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
            <MessageSquare className="w-5 h-5 text-purple-500 flex-shrink-0" />
            <span className="text-left">Insights personalizados com IA</span>
          </div>
        </div>

        {/* Botão de ação */}
        <div className="pt-4">
          <Button
            onClick={onCreateNewChat}
            disabled={isCreatingSession}
            size="lg"
            className="w-full flex items-center gap-2"
          >
            {isCreatingSession ? (
              <>
                <div className="w-4 h-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                Criando conversa...
              </>
            ) : (
              <>
                <PlusCircle className="w-5 h-5" />
                Iniciar Nova Conversa
              </>
            )}
          </Button>
        </div>

        {/* Dica adicional */}
        {/* <div className="pt-2">
          <p className="text-xs text-muted-foreground">
            Ou se preferir escolha uma das suas conversas passadas
          </p>
        </div> */}
      </div>
    </div>
  );
}
