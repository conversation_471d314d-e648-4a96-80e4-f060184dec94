// app/api/cotacoes-historicas-b3/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createServerCompatClient } from '@/utils/supabase/server-compat';

// Definindo os headers CORS como constante (não exportada)
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, apikey'
};

export async function OPTIONS() {
  return NextResponse.json({}, { headers: corsHeaders });
}

export async function GET(request: NextRequest) {
  // Verificar autenticação
  const authHeader = request.headers.get('Authorization');

  if (!authHeader) {
    return NextResponse.json(
      { error: 'Cabeçalho de autorização não fornecido' },
      { status: 401, headers: corsHeaders }
    );
  }

  const token = authHeader.replace('Bearer ', '');

  // Create a cookie store compatible with our server-compat client
  const cookieStore = {
    getAll: () => [],
    set: () => {}
  };

  const supabase = createServerCompatClient(cookieStore);

  // Verificar o usuário
  const { data: { user }, error: userError } = await supabase.auth.getUser(token);

  if (userError || !user) {
    return NextResponse.json(
      { error: 'Token inválido' },
      { status: 401, headers: corsHeaders }
    );
  }

  // Obter o parâmetro data_referencia, se fornecido
  const url = new URL(request.url);
  const dataReferencia = url.searchParams.get('data_referencia');

  // Construir a consulta para cotacoes_historicas_b3
  let query = supabase
    .from('cotacoes_historicas_b3')
    .select('codbdi, codisi, codneg, data_pregao, datven, dismes, especi, fatcot, indopc, modref, nomres, prazot, preabe, preexe, premax, premed, premin, preofc, preofv, preult, ptoexe, quatot, tipo_registro, totneg, tpmerc, voltot');

  // Adicionar filtro por data_referencia, se fornecido
  if (dataReferencia) {
    query = query.eq('data_pregao', dataReferencia);
  }

  const { data, error } = await query;

  if (error) {
    return NextResponse.json(
      { error: error.message },
      { status: 500, headers: corsHeaders }
    );
  }

  return NextResponse.json(
    { data },
    { headers: corsHeaders }
  );
}