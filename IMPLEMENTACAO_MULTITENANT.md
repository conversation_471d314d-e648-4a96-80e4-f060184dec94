# Implementação Multi-Tenant - PrimeAI

## Arquivos Criados/Modificados

### ✅ Arquivos SQL
- `sql/multitenant-setup.sql` - Script completo para configurar o banco

### ✅ Middleware Atualizado
- `utils/supabase/middleware.ts` - Adicionada lógica de subdomínios

### ✅ Novos Componentes e APIs
- `app/tenant-not-found/page.tsx` - Página de erro para tenant inexistente
- `app/api/tenant/current/route.ts` - API para dados do tenant atual
- `app/api/admin/tenants/route.ts` - API para gerenciar tenants
- `lib/tenant-context.tsx` - Context provider para tenant
- `components/tenant-logo.tsx` - Componente de logo dinâmico

### ✅ Configuração
- `next.config.ts` - Headers de segurança adicionados

## Passos para Implementação

### 1. Configurar Banco de Dados
```bash
# Execute o script SQL no Supabase
psql -h your-supabase-host -U postgres -d postgres -f sql/multitenant-setup.sql
```

### 2. Variáveis de Ambiente
Adicione ao `.env.local`:
```bash
# Domínio principal
NEXT_PUBLIC_ROOT_DOMAIN=vascofa.shop

# Supabase (já existentes)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### 3. Atualizar Layout Principal
Modifique `app/layout.tsx` para incluir o TenantProvider:

```tsx
import { TenantProvider } from '@/lib/tenant-context';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="pt-BR">
      <body>
        <TenantProvider>
          {children}
        </TenantProvider>
      </body>
    </html>
  );
}
```

### 4. Atualizar Componentes Existentes
Substitua logos/títulos fixos pelo componente `TenantLogo`:

```tsx
import { TenantLogo } from '@/components/tenant-logo';

// Em vez de:
<h1>PrimeAI</h1>

// Use:
<TenantLogo />
```

### 5. Configurar DNS (Produção)
No seu provedor de DNS:
```
A     vascofa.shop          → IP do Vercel
CNAME *.vascofa.shop        → vascofa.shop
```

### 6. Configurar Vercel
1. Adicionar domínio `vascofa.shop` no projeto Vercel
2. Configurar variáveis de ambiente no dashboard
3. Deploy da aplicação

## Testando Localmente

### 1. Configurar hosts locais
Adicione ao arquivo `/etc/hosts` (Linux/Mac) ou `C:\Windows\System32\drivers\etc\hosts` (Windows):
```
127.0.0.1 demo.localhost
127.0.0.1 empresa1.localhost
127.0.0.1 empresa2.localhost
```

### 2. Criar tenants de teste
Execute no Supabase SQL Editor:
```sql
INSERT INTO tenants (subdomain, name, primary_color, secondary_color) VALUES
('demo', 'Empresa Demo', '#1f2937', '#f3f4f6'),
('empresa1', 'Empresa 1', '#dc2626', '#fef2f2'),
('empresa2', 'Empresa 2', '#059669', '#f0fdf4');
```

### 3. Testar URLs
- `http://localhost:3000` - Domínio principal
- `http://demo.localhost:3000` - Tenant demo
- `http://empresa1.localhost:3000` - Tenant empresa1
- `http://empresa2.localhost:3000` - Tenant empresa2

## Funcionalidades Implementadas

### ✅ Roteamento por Subdomínio
- Detecção automática de subdomínios
- Suporte para desenvolvimento local e produção
- Suporte para preview deployments do Vercel

### ✅ Isolamento de Dados
- Row Level Security (RLS) no Supabase
- Contexto de tenant definido automaticamente
- Queries filtradas por tenant_id

### ✅ Personalização Visual
- Cores personalizadas por tenant
- Logo customizado
- CSS personalizado (opcional)

### ✅ Segurança
- Validação de tenant ativo
- Bloqueio de rotas admin em subdomínios
- Headers de segurança

### ✅ APIs de Gerenciamento
- Listar tenants
- Criar novos tenants
- Obter dados do tenant atual

## Próximos Passos Opcionais

### Painel Administrativo
Criar `app/admin/tenants/page.tsx` para gerenciar tenants via interface web.

### Métricas por Tenant
Implementar dashboard com métricas específicas por tenant.

### Cache por Tenant
Adicionar Redis para cache isolado por tenant.

## Troubleshooting

### Tenant não encontrado
- Verificar se o tenant existe na tabela `tenants`
- Verificar se `is_active = true`
- Verificar configuração de DNS

### RLS não funcionando
- Verificar se as políticas estão habilitadas
- Verificar se a função `set_current_tenant` está sendo chamada
- Verificar logs do Supabase

### Subdomínios não funcionando localmente
- Verificar arquivo hosts
- Verificar se está usando `.localhost` (não `.local`)
- Reiniciar navegador após alterar hosts

## Comandos Úteis

```bash
# Verificar tenants no banco
psql -c "SELECT subdomain, name, is_active FROM tenants;"

# Verificar RLS
psql -c "SELECT schemaname, tablename, rowsecurity FROM pg_tables WHERE rowsecurity = true;"

# Limpar cache do navegador
# Chrome: Ctrl+Shift+R
# Firefox: Ctrl+F5
```
