"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import axios from "axios";
import { ChatMessage } from "./types";

export interface Session {
  id: string;
  name: string;
  createdAt: string;
  messages: ChatMessage[];
  sessionCode?: string; // Código curto para exibição
}

interface SessionContextProps {
  sessions: Session[];
  currentSessionId: string | null;
  isCreatingSession: boolean;
  createNewSession: (userId: string) => Promise<string>;
  deleteSession: (userId: string, sessionId: string) => Promise<void>;
  setCurrentSessionId: (sessionId: string) => void;
  getCurrentSession: () => Session | undefined;
  addMessageToSession: (sessionId: string, message: ChatMessage) => void;
  updateMessageInSession: (sessionId: string, messageId: string, updatedMessage: ChatMessage) => void;
  updateSessionName: (sessionId: string, name: string) => void;
}

const SessionContext = createContext<SessionContextProps | undefined>(undefined);

export const useSession = () => {
  const context = useContext(SessionContext);
  if (!context) {
    throw new Error("useSession must be used within a SessionProvider");
  }
  return context;
};

interface SessionProviderProps {
  children: React.ReactNode;
}

export const SessionProvider: React.FC<SessionProviderProps> = ({ children }) => {
  const [sessions, setSessions] = useState<Session[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [isCreatingSession, setIsCreatingSession] = useState(false);

  // Carregar sessões do localStorage ao iniciar
  useEffect(() => {
    const savedSessions = localStorage.getItem("chat_sessions");
    if (savedSessions) {
      setSessions(JSON.parse(savedSessions));
    }

    // Não carregamos automaticamente a última sessão
    // O usuário precisa selecionar uma sessão ou criar uma nova
  }, []);

  // Salvar sessões no localStorage quando mudar
  useEffect(() => {
    if (sessions.length > 0) {
      localStorage.setItem("chat_sessions", JSON.stringify(sessions));
    }
  }, [sessions]);

  // Não salvamos a sessão atual no localStorage
  // O usuário sempre deve escolher ou criar uma nova sessão

  const createNewSession = async (userId: string) => {
    setIsCreatingSession(true);
    try {
      // Chamar a API para criar a sessão - agora o ID será gerado pelo servidor
      const response = await axios.post("/api/session", {
        userId
      });

      if (response.data.success && response.data.sessionId) {
        const sessionId = response.data.sessionId;

        const newSession: Session = {
          id: sessionId,
          name: `Conversa iniciada ${new Date().toLocaleString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
          })}`,
          createdAt: new Date().toISOString(),
          messages: [],
          sessionCode: sessionId.substring(0, 8).toUpperCase() // Código curto para exibição
        };

        setSessions(prev => [...prev, newSession]);
        setCurrentSessionId(sessionId);
        return sessionId;
      } else {
        throw new Error(response.data.message || "Erro ao criar sessão");
      }
    } catch (error) {
      console.error("Erro ao criar nova sessão:", error);
      throw error;
    } finally {
      setIsCreatingSession(false);
    }
  };

  const deleteSession = async (userId: string, sessionId: string) => {
    try {
      // Chamar a API para deletar a sessão
      const response = await axios.delete(`/api/session?userId=${userId}&sessionId=${sessionId}`);

      if (response.data.success) {
        setSessions(prev => prev.filter(session => session.id !== sessionId));

        // Se a sessão atual foi deletada, selecionar outra ou criar uma nova
        if (currentSessionId === sessionId) {
          const remainingSessions = sessions.filter(s => s.id !== sessionId);
          if (remainingSessions.length > 0) {
            setCurrentSessionId(remainingSessions[0].id);
          } else {
            setCurrentSessionId(null);
          }
        }
      } else {
        throw new Error(response.data.message || "Erro ao deletar sessão");
      }
    } catch (error) {
      console.error("Erro ao deletar sessão:", error);
      throw error;
    }
  };

  const getCurrentSession = () => {
    return sessions.find(session => session.id === currentSessionId);
  };

  const addMessageToSession = (sessionId: string, message: ChatMessage) => {
    setSessions(prev =>
      prev.map(session =>
        session.id === sessionId
          ? { ...session, messages: [...session.messages, message] }
          : session
      )
    );
  };

  const updateMessageInSession = (sessionId: string, messageId: string, updatedMessage: ChatMessage) => {
    setSessions(prev =>
      prev.map(session =>
        session.id === sessionId
          ? {
              ...session,
              messages: session.messages.map(msg =>
                msg.id === messageId ? updatedMessage : msg
              )
            }
          : session
      )
    );
  };

  const updateSessionName = (sessionId: string, name: string) => {
    setSessions(prev =>
      prev.map(session =>
        session.id === sessionId
          ? { ...session, name }
          : session
      )
    );
  };

  return (
    <SessionContext.Provider
      value={{
        sessions,
        currentSessionId,
        isCreatingSession,
        createNewSession,
        deleteSession,
        setCurrentSessionId,
        getCurrentSession,
        addMessageToSession,
        updateMessageInSession,
        updateSessionName
      }}
    >
      {children}
    </SessionContext.Provider>
  );
};
