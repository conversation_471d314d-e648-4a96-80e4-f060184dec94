import { toast } from "sonner";
import * as XLSX from "xlsx";
import { Database } from "@/types/database";
import { createClient } from "@/utils/supabase/client";

/**
 * Serviço responsável por exportar dados para Excel
 */
export class ExportService {
  /**
   * Exporta os dados das bases selecionadas para um arquivo Excel
   * @param selectedDatabases IDs das bases de dados selecionadas
   * @param databasesConfig Configuração completa das bases de dados
   * @param dataReferencia Data de referência opcional para filtrar os dados
   * @param accessToken Token de acesso para autenticação nas APIs
   * @returns Promise<boolean> Indica se a exportação foi bem-sucedida
   */
  static async exportToExcel(
    selectedDatabases: string[],
    databasesConfig: Database[],
    dataReferencia?: string,
    accessToken?: string | null
  ): Promise<boolean> {
    if (selectedDatabases.length === 0) {
      toast.error("Selecione pelo menos uma base de dados para exportar");
      return false;
    }

    if (!accessToken) {
      console.error("[ExportService] Token de acesso não fornecido");
      toast.error("Erro de autenticação. Faça login novamente.");
      return false;
    }

    console.log("[ExportService] Iniciando exportação...", {
      selectedDatabases,
      dataReferencia,
      hasToken: !!accessToken // ✅ Seguro: apenas boolean, não expõe o token
    });

    const workbook = XLSX.utils.book_new();
    let hasData = false;
    let hasError = false;

    try {
      // Para cada base selecionada, buscar os dados e adicionar à planilha
      for (const dbId of selectedDatabases) {
        const database = databasesConfig.find((db) => db.id === dbId);
        if (!database) {
          console.warn(`[ExportService] Database não encontrada: ${dbId}`);
          continue;
        }

        console.log(`[ExportService] Buscando dados para: ${database.name}`);

        try {
          // Buscar dados da API
          const data = await this.fetchDatabaseData(database, dataReferencia, accessToken);

          if (data && data.length > 0) {
            console.log(`[ExportService] Dados recebidos para ${database.name}: ${data.length} registros`);

            // Remover campos que não devem ser exportados
            const filteredData = data.map(item => {
              const { id, created_at, ...rest } = item;
              return rest;
            });

            // Criar uma planilha para esta base
            const worksheet = XLSX.utils.json_to_sheet(filteredData);

            // Adicionar a planilha ao workbook com o nome da base
            XLSX.utils.book_append_sheet(workbook, worksheet, database.name);
            hasData = true;
          } else {
            console.warn(`[ExportService] Nenhum dado encontrado para ${database.name}`);
            toast.warning(`Nenhum dado encontrado para ${database.name}`);
          }
        } catch (error) {
          console.error(`[ExportService] Erro ao buscar dados de ${database.name}:`, error);
          toast.error(`Falha ao exportar ${database.name}`);
          hasError = true;
        }
      }

      if (hasData) {
        // Exportar o workbook como um arquivo .xlsx
        const date = new Date().toISOString().split("T")[0];
        const fileName = dataReferencia
          ? `dados_financeiros_${dataReferencia}.xlsx`
          : `dados_financeiros_${date}.xlsx`;

        console.log(`[ExportService] Gerando arquivo Excel: ${fileName}`);
        XLSX.writeFile(workbook, fileName);

        if (!hasError) {
          toast.success("Exportação concluída com sucesso!");
        } else {
          toast.warning("Exportação concluída com alguns erros.");
        }

        return true;
      } else {
        console.error("[ExportService] Nenhum dado encontrado para exportar");
        toast.error("Nenhum dado encontrado para exportar");
        return false;
      }
    } catch (error) {
      console.error("[ExportService] Erro ao exportar dados:", error);
      toast.error("Erro ao exportar dados. Tente novamente.");
      return false;
    }
  }

  /**
   * Busca os dados de uma base específica
   * @param database Configuração da base de dados
   * @param dataReferencia Data de referência opcional
   * @param accessToken Token de acesso para autenticação
   * @returns Promise<any[]> Dados da base
   */
  private static async fetchDatabaseData(
    database: Database,
    dataReferencia?: string,
    accessToken?: string | null
  ): Promise<any[]> {
    console.log(`[ExportService] fetchDatabaseData iniciando para ${database.name}`, {
      endpoint: database.endpoint,
      dataReferencia,
      hasToken: !!accessToken
    });

    if (!accessToken) {
      console.error("[ExportService] Token de acesso não fornecido");
      throw new Error("Token de autenticação não encontrado");
    }

    try {
      // Obter o endpoint baseado no ID da base de dados
      const endpointPath = database.endpoint;

      // Fazer uma requisição GET direta usando fetch nativo
      const url = new URL(endpointPath, window.location.origin);

      // Adicionar parâmetro de data_referencia se fornecido
      if (dataReferencia) {
        url.searchParams.append('data_referencia', dataReferencia);
      }

      console.log(`[ExportService] Fazendo requisição para: ${url.toString()}`);

      // Fazer a requisição com o token de acesso no cabeçalho
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`
        }
      });

      console.log(`[ExportService] Resposta recebida, status: ${response.status}`);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error("[ExportService] Erro na resposta da API:", {
          status: response.status,
          error: errorData
        });
        throw new Error(
          errorData.error ||
          `Erro ao buscar dados de ${database.name}: ${response.status}`
        );
      }

      const result = await response.json();
      console.log(`[ExportService] Dados recebidos com sucesso para ${database.name}`);

      // Verificar se a resposta tem a estrutura esperada
      if (!result || !result.data) {
        console.error("[ExportService] Formato de resposta inválido:", result);
        throw new Error('Formato de resposta inválido');
      }

      return result.data;
    } catch (error) {
      console.error(`[ExportService] Erro ao buscar dados de ${database.name}:`, error);
      toast.error(`Erro ao buscar ${database.name}: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
      throw error;
    }
  }
}