import { Message } from "@/components/form-message";
import ResetPasswordFormClient from "@/components/auth/reset-password-form-client";
import { ResetPasswordFormStatic } from "@/components/auth/static-forms";
import { MobileSafe } from "@/components/ui/mobile-safe";

export default async function ResetPassword(props: {
  searchParams: Promise<Message>;
}) {
  const searchParams = await props.searchParams;
  return (
    <div className="w-full flex-1 flex flex-col items-center justify-center min-h-[calc(100vh-8rem)]">
      <div className="w-full max-w-md px-8 py-12">
        <MobileSafe fallback={<ResetPasswordFormStatic />}>
          <ResetPasswordFormClient initialMessage={searchParams} />
        </MobileSafe>
      </div>
    </div>
  );
}
