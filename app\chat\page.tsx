"use client";

import ChatInterface from "@/components/chat/chat-interface";
import SessionSidebar from "@/components/chat/session-sidebar";
import { SessionProvider } from "@/components/chat/session-context";
import { SidebarProvider, useSidebar } from "@/components/chat/sidebar-context";
import { useEffect, useState } from "react";
import { createClient } from "@/utils/supabase/client";
import { useIsMobile } from "@/components/hooks/use-mobile";
import { AppConfig } from "@/lib/config/app-config";

// Componente interno que usa o hook useSidebar
function ChatLayout({ userId }: { userId: string }) {
  const { isExpanded } = useSidebar();
  const isMobile = useIsMobile();

  return (
    <SessionProvider>
      {/* Container principal com os dois elementos: sidebar secundário e conteúdo */}
      <div className="flex h-screen w-full">
        {/* Sidebar secundário (sessões) - visível apenas em desktop e se habilitado */}
        {!isMobile && AppConfig.enableSessionHistory && (
          <div
            className={`h-screen flex-shrink-0 border-r border-border transition-all duration-300 ease-in-out ${
              isExpanded ? 'w-64' : 'w-16'
            }`}
          >
            <SessionSidebar userId={userId} />
          </div>
        )}

        {/* Área principal do chat (com scroll próprio) - se expande dinamicamente */}
        <div className="flex-1 h-screen overflow-hidden">
          <ChatInterface userId={userId} />
        </div>
      </div>
    </SessionProvider>
  );
}

export default function ChatPage() {
  const [userId, setUserId] = useState<string | null>(null);
  const supabase = createClient();

  useEffect(() => {
    const getUser = async () => {
      const { data } = await supabase.auth.getUser();
      if (data.user) {
        setUserId(data.user.id);
      }
    };

    getUser();
  }, [supabase.auth]);

  if (!userId) {
    return (
      <div className="flex items-center justify-center h-full w-full">
        <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
      </div>
    );
  }

  return (
    <SidebarProvider>
      <ChatLayout userId={userId} />
    </SidebarProvider>
  );
}
