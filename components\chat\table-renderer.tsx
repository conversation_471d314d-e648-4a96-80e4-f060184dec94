"use client";

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useIsMobile } from "@/components/hooks/use-mobile";

interface TableRendererProps {
  content: string;
}

interface ParsedTable {
  headers: string[];
  rows: string[][];
}

export default function TableRenderer({ content }: TableRendererProps) {
  const isMobile = useIsMobile();

  const parseTableFromText = (text: string): ParsedTable | null => {
    const lines = text.split('\n');

    // Encontrar linhas que contêm pipes
    const tableLines: string[] = [];
    let separatorIndex = -1;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Linha com pipes (dados da tabela)
      if (line.includes('|') && (line.match(/\|/g) || []).length >= 2) {
        tableLines.push(line);
      }
      // Linha separadora (traços e pipes) - mais flexível
      else if ((/^[\-\|\s]+$/.test(line) || /^\|?[\-\s]+\|[\-\|\s]*$/.test(line) || /^[\-]{3,}$/.test(line)) && tableLines.length === 1) {
        separatorIndex = i;
      }
    }

    // Precisa ter pelo menos header + dados (separador é opcional)
    if (tableLines.length < 2) {
      return null;
    }

    // Processar as linhas da tabela
    const processedLines = tableLines.map(line => {
      // Dividir por pipes e limpar células
      let cells = line.split('|').map(cell => cell.trim());

      // Remover células vazias do início e fim (pipes opcionais)
      if (cells.length > 0 && cells[0] === '') {
        cells = cells.slice(1);
      }
      if (cells.length > 0 && cells[cells.length - 1] === '') {
        cells = cells.slice(0, -1);
      }

      return cells.filter(cell => cell.length > 0);
    }).filter(line => {
      // Remover linhas que são apenas separadores (traços)
      return line.length > 0 && !line.every(cell => /^[\-\s]*$/.test(cell));
    });

    if (processedLines.length < 2) return null;

    // Primeira linha são os headers
    const headers = processedLines[0];
    // Resto são os dados
    const rows = processedLines.slice(1);

    // Validar que todas as linhas têm o mesmo número de colunas
    const columnCount = headers.length;
    if (columnCount === 0) return null;

    const validRows = rows.filter(row => row.length === columnCount);

    if (validRows.length === 0) return null;

    return {
      headers,
      rows: validRows
    };
  };

  const extractTableAndRemainingText = (text: string): { table: ParsedTable | null; remainingText: string } => {
    // Tentar parsear a tabela diretamente
    const table = parseTableFromText(text);

    if (!table) {
      return { table: null, remainingText: text };
    }

    // Se encontrou tabela, extrair o texto que não é tabela
    const lines = text.split('\n');
    const tableLines: number[] = [];
    let separatorLine = -1;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      if (line.includes('|') && (line.match(/\|/g) || []).length >= 2) {
        tableLines.push(i);
      } else if (/^[\-\|\s]+$/.test(line) || /^\|?[\-\s]+\|[\-\|\s]*$/.test(line) || /^[\-]{3,}$/.test(line)) {
        separatorLine = i;
      }
    }

    if (tableLines.length === 0) {
      return { table: null, remainingText: text };
    }

    // Texto antes da tabela
    const beforeTable = lines.slice(0, tableLines[0]).join('\n').trim();
    // Texto depois da tabela
    const afterTable = lines.slice(tableLines[tableLines.length - 1] + 1).join('\n').trim();

    const remainingText = [beforeTable, afterTable].filter(part => part.length > 0).join('\n\n');

    return { table, remainingText };
  };

  const { table, remainingText } = extractTableAndRemainingText(content);

  if (!table) {
    return <span>{content}</span>;
  }

  return (
    <div className="space-y-4">
      {/* Texto antes da tabela */}
      {remainingText && remainingText !== content && (
        <div className="whitespace-pre-wrap">{remainingText}</div>
      )}

      {/* Tabela */}
      <div className="rounded-lg border overflow-hidden bg-background shadow-sm">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-muted/70 hover:bg-muted/70 border-b-2">
                {table.headers.map((header, index) => (
                  <TableHead
                    key={index}
                    className={`font-semibold text-foreground text-center ${
                      isMobile ? 'text-xs py-2 px-2 min-w-[80px]' : 'text-sm py-3 px-4'
                    }`}
                  >
                    {header}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {table.rows.map((row, rowIndex) => (
                <TableRow key={rowIndex} className="hover:bg-muted/20 border-b transition-colors">
                  {row.map((cell, cellIndex) => (
                    <TableCell
                      key={cellIndex}
                      className={isMobile ? 'py-2 px-2 text-xs' : 'py-3 px-4 text-sm'}
                    >
                      {/* Detectar se é um número para alinhamento à direita */}
                      <span className={/^[\d.,]+$/.test(cell.trim()) ? "text-right block font-mono" : "text-center block"}>
                        {cell}
                      </span>
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
