"use client";

import { usePathname } from "next/navigation";
import HeaderAuthWrapper from "@/components/header-auth-wrapper";

export default function PublicLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const isHomePage = pathname === "/";

  // Se for a página inicial, não mostrar o header
  if (isHomePage) {
    return (
      <div className="w-full min-h-screen">
        {children}
      </div>
    );
  }

  // Para outras páginas, mostrar o header
  return (
    <>
      <nav className="w-full flex justify-center border-b border-b-foreground/10 h-16">
        <div className="w-full max-w-5xl flex justify-end items-center p-3 px-5 text-sm">
          <HeaderAuthWrapper />
        </div>
      </nav>
      <div className="w-full flex-1 flex flex-col">
        {children}
      </div>
    </>
  );
}
