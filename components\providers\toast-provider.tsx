"use client";

import { Toaster } from "sonner";
import { useIsMobile } from "@/components/hooks/use-mobile";

export function ToastProvider() {
  const isMobile = useIsMobile();

  return (
    <Toaster
      position={isMobile ? "top-center" : "top-right"}
      richColors
      closeButton
      expand={true}
      visibleToasts={isMobile ? 3 : 5}
      toastOptions={{
        style: {
          marginTop: isMobile ? '60px' : '0px', // Espaço para não sobrepor header mobile
        },
        className: isMobile ? 'mobile-toast' : '',
      }}
    />
  );
}