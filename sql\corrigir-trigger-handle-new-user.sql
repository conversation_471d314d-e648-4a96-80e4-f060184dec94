-- =====================================================
-- CORRIGIR TRIGGER HANDLE_NEW_USER
-- =====================================================

-- 1. REMOVER TRIGGER EXISTENTE
-- =====================================================
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS handle_new_user();

-- 2. CRIAR FUNÇÃO HANDLE_NEW_USER ROBUSTA
-- =====================================================
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    current_tenant_id UUID;
    user_subdomain TEXT;
    default_tenant_id UUID;
BEGIN
    -- Log para debug
    RAISE NOTICE 'Trigger handle_new_user executado para usuário: %', NEW.id;
    
    -- Obter subdomínio do metadata do usuário
    user_subdomain := NEW.raw_user_meta_data->>'subdomain';
    RAISE NOTICE 'Subdomínio do metadata: %', user_subdomain;
    
    -- Buscar tenant_id pelo subdomínio
    IF user_subdomain IS NOT NULL THEN
        SELECT id INTO current_tenant_id
        FROM app.tenants 
        WHERE subdomain = user_subdomain 
        AND is_active = true;
        
        RAISE NOTICE 'Tenant encontrado pelo subdomínio %: %', user_subdomain, current_tenant_id;
    END IF;
    
    -- Se não encontrou tenant pelo subdomínio, tentar contexto RLS
    IF current_tenant_id IS NULL THEN
        BEGIN
            current_tenant_id := app.get_current_tenant_id();
            RAISE NOTICE 'Tenant do contexto RLS: %', current_tenant_id;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Erro ao obter tenant do contexto RLS: %', SQLERRM;
        END;
    END IF;
    
    -- Se ainda não encontrou, usar tenant padrão (Vasco para desenvolvimento)
    IF current_tenant_id IS NULL THEN
        SELECT id INTO default_tenant_id
        FROM app.tenants 
        WHERE subdomain = 'vasco' 
        AND is_active = true
        LIMIT 1;
        
        IF default_tenant_id IS NOT NULL THEN
            current_tenant_id := default_tenant_id;
            RAISE NOTICE 'Usando tenant padrão (vasco): %', current_tenant_id;
        END IF;
    END IF;
    
    -- Se ainda não encontrou, usar primeiro tenant ativo
    IF current_tenant_id IS NULL THEN
        SELECT id INTO current_tenant_id
        FROM app.tenants 
        WHERE is_active = true
        ORDER BY created_at ASC
        LIMIT 1;
        
        RAISE NOTICE 'Usando primeiro tenant ativo: %', current_tenant_id;
    END IF;
    
    -- Se ainda não tem tenant, criar erro
    IF current_tenant_id IS NULL THEN
        RAISE EXCEPTION 'Nenhum tenant ativo encontrado para criar usuário';
    END IF;
    
    -- Inserir usuário com tenant_id
    BEGIN
        INSERT INTO app.users (user_id, full_name, email, tenant_id, credits, created_at, updated_at)
        VALUES (
            NEW.id,
            COALESCE(NEW.raw_user_meta_data->>'full_name', 'Usuário'),
            NEW.email,
            current_tenant_id,
            100, -- Créditos iniciais
            NEW.created_at,
            NOW()
        );
        
        RAISE NOTICE 'Usuário criado com sucesso em app.users com tenant_id: %', current_tenant_id;
        
    EXCEPTION WHEN unique_violation THEN
        RAISE NOTICE 'Usuário já existe em app.users, atualizando tenant_id se necessário';
        
        -- Atualizar tenant_id se necessário
        UPDATE app.users 
        SET tenant_id = current_tenant_id,
            updated_at = NOW()
        WHERE user_id = NEW.id
        AND (tenant_id IS NULL OR tenant_id != current_tenant_id);
        
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Erro ao inserir usuário em app.users: %', SQLERRM;
    END;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. CRIAR TRIGGER
-- =====================================================
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- 4. COMENTÁRIOS
-- =====================================================
COMMENT ON FUNCTION handle_new_user() IS 'Trigger function para criar usuário em app.users com tenant_id baseado no metadata subdomain, contexto RLS ou tenant padrão';
COMMENT ON TRIGGER on_auth_user_created ON auth.users IS 'Trigger para criar usuário em app.users automaticamente após inserção em auth.users';

-- 5. VERIFICAR TRIGGER CRIADO
-- =====================================================
SELECT 
    trigger_name,
    event_manipulation,
    action_timing,
    action_statement
FROM information_schema.triggers 
WHERE event_object_schema = 'auth'
AND event_object_table = 'users'
AND trigger_name = 'on_auth_user_created';

-- 6. TESTAR FUNÇÃO MANUALMENTE
-- =====================================================
DO $$
DECLARE
    test_user_id UUID := '38cc3c24-7d83-4194-bcc9-68a501ceff2d';
    vasco_tenant_id UUID;
BEGIN
    -- Buscar tenant Vasco
    SELECT id INTO vasco_tenant_id FROM app.tenants WHERE subdomain = 'vasco';
    
    -- Simular inserção de usuário
    RAISE NOTICE 'Testando criação de usuário com tenant_id: %', vasco_tenant_id;
    
    -- Verificar se usuário já existe
    IF EXISTS (SELECT 1 FROM app.users WHERE user_id = test_user_id) THEN
        RAISE NOTICE 'Usuário já existe em app.users';
    ELSE
        RAISE NOTICE 'Usuário não existe, seria criado pelo trigger';
    END IF;
END $$;

-- =====================================================
-- RESULTADO ESPERADO:
-- - Trigger criado com sucesso
-- - Função robusta com múltiplos fallbacks
-- - Logs detalhados para debug
-- - Usuários futuros criados automaticamente
-- =====================================================
