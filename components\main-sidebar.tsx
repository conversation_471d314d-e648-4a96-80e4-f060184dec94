"use client";

import React, { useState, useEffect } from "react";
import { LogOut, MessageSquare, ChevronRight, ChevronLeft, CreditCard } from "lucide-react";
import Link from "next/link";
import { signOutClient } from "@/app/client-actions";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { createClient } from "@/utils/supabase/client";
import { useRouter, usePathname } from "next/navigation";
import { useIsMobile } from "@/components/hooks/use-mobile";
import CreditsDisplay from "@/components/credits-display";
import { TenantLogo } from "@/components/tenant-logo";


interface MainSidebarProps {
  userId: string;
  onNavigate?: () => void;
}

export default function MainSidebar({ userId, onNavigate }: MainSidebarProps) {
  const [expanded, setExpanded] = useState(false);
  const [userEmail, setUserEmail] = useState<string>("");
  const [currentUserId, setCurrentUserId] = useState<string>("");
  const router = useRouter();
  const pathname = usePathname();
  const supabase = createClient();
  const isMobile = useIsMobile();

  // Em mobile, sempre considerar como expandido
  const isExpanded = isMobile || expanded;

  // Recuperar estado do sidebar do localStorage
  useEffect(() => {
    const savedState = localStorage.getItem("sidebar-expanded");
    if (savedState) {
      setExpanded(savedState === "true");
    }
  }, []);

  // Buscar informações do usuário
  useEffect(() => {
    const getUser = async () => {
      const { data } = await supabase.auth.getUser();
      if (data.user) {
        setUserEmail(data.user.email || "");
        setCurrentUserId(data.user.id);
      }
    };
    getUser();
  }, [supabase.auth]);

  const toggleSidebar = () => {
    const newState = !expanded;
    setExpanded(newState);
    localStorage.setItem("sidebar-expanded", String(newState));
  };

  const handleSignOut = async () => {
    try {
      await signOutClient();
      router.push("/sign-in");
    } catch (error) {
      // Erro no logout é tratado silenciosamente por segurança
    }
  };

  return (
    <TooltipProvider delayDuration={300}>
      <aside
        className={cn(
          "h-full bg-card border-r border-border flex flex-col justify-between z-40 transition-all duration-300",
          isExpanded ? "w-56" : "w-16"
        )}
      >
        <div className="flex flex-col">
          {/* Cabeçalho */}
          <div className={cn(
            "flex items-center h-14",
            isExpanded ? "justify-between px-4" : "justify-center px-2"
          )}>
            {isMobile ? (
              /* Layout mobile: sempre expandido, sem botão de recolher */
              <div className="flex items-center gap-3 w-full">
                <TenantLogo className="flex items-center gap-3" />
              </div>
            ) : isExpanded ? (
              <>
                {/* Desktop expandido: Logo + Nome à esquerda, botão à direita */}
                <div className="flex items-center gap-3">
                  <TenantLogo className="flex items-center gap-3" />
                </div>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 shrink-0"
                      onClick={toggleSidebar}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    Retrair sidebar
                  </TooltipContent>
                </Tooltip>
              </>
            ) : (
              <>
                {/* Desktop retraído: Logo centralizada e clicável */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      className="w-10 h-10 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-bold hover:bg-primary/90 transition-colors"
                      onClick={toggleSidebar}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    Expandir
                  </TooltipContent>
                </Tooltip>
              </>
            )}
          </div>

          {/* Navegação */}
          <nav className="flex flex-col gap-2 p-3">
            <Tooltip>
              <TooltipTrigger asChild>
                <Link
                  href="/chat"
                  className={cn(
                    "flex items-center gap-3 rounded-md px-3 py-2 transition-colors",
                    pathname === "/chat"
                      ? "bg-accent text-foreground font-medium"
                      : "text-muted-foreground hover:text-foreground hover:bg-accent",
                    !isExpanded && !isMobile && "justify-center"
                  )}
                  onClick={() => isMobile && onNavigate?.()}
                >
                  <MessageSquare className="h-5 w-5 shrink-0" />
                  {(isExpanded || isMobile) && <span>Chat</span>}
                </Link>
              </TooltipTrigger>
              {!isExpanded && !isMobile && <TooltipContent side="right">Chat</TooltipContent>}
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Link
                  href="/uso"
                  className={cn(
                    "flex items-center gap-3 rounded-md px-3 py-2 transition-colors",
                    pathname === "/uso"
                      ? "bg-accent text-foreground font-medium"
                      : "text-muted-foreground hover:text-foreground hover:bg-accent",
                    !isExpanded && !isMobile && "justify-center"
                  )}
                  onClick={() => isMobile && onNavigate?.()}
                >
                  <CreditCard className="h-5 w-5 shrink-0" />
                  {(isExpanded || isMobile) && <span>Uso</span>}
                </Link>
              </TooltipTrigger>
              {!isExpanded && !isMobile && <TooltipContent side="right">Uso</TooltipContent>}
            </Tooltip>
          </nav>

          {/* Espaço adicional para mobile */}
          {isMobile && (
            <div className="px-3 py-2">
              <div className="text-xs text-muted-foreground mb-2">Versão</div>
              <div className="text-xs bg-muted/30 px-2 py-1 rounded">PrimeAI v1.0</div>
            </div>
          )}
        </div>

        {/* Rodapé */}
        <div className={cn("p-3", isMobile ? "pb-8" : "safe-bottom")}>
          {/* Créditos do usuário */}
          {(isExpanded || isMobile) && currentUserId && (
            <div className="mb-3 px-3 py-2 rounded-md bg-muted/50">
              <div className="text-xs text-muted-foreground mb-2">Seus créditos:</div>
              <CreditsDisplay />
            </div>
          )}

          {/* Informações do usuário */}
          {(isExpanded || isMobile) && userEmail && (
            <div className="mb-3 px-3 py-2 rounded-md bg-muted/50">
              <div className="text-xs text-muted-foreground">Logado como:</div>
              <div className={`font-medium ${isMobile ? 'text-xs break-all' : 'text-sm truncate'}`}>
                {userEmail}
              </div>
            </div>
          )}

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                onClick={handleSignOut}
                className={cn(
                  "w-full justify-start text-muted-foreground hover:text-foreground hover:bg-accent",
                  !isExpanded && !isMobile && "justify-center px-2"
                )}
              >
                <LogOut className="h-5 w-5 shrink-0" />
                {(isExpanded || isMobile) && <span className="ml-3">Sair</span>}
              </Button>
            </TooltipTrigger>
            {!isExpanded && !isMobile && <TooltipContent side="right">Sair</TooltipContent>}
          </Tooltip>
        </div>
      </aside>
    </TooltipProvider>
  );
}
