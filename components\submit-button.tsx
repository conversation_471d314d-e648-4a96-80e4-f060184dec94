"use client";

import { But<PERSON> } from "@/components/ui/button";
import { type ComponentProps } from "react";
import { useState } from "react";

type Props = ComponentProps<typeof Button> & {
  pendingText?: string;
  formAction?: (formData: FormData) => Promise<void>;
};

export function SubmitButton({
  children,
  pendingText = "Enviando...",
  formAction,
  ...props
}: Props) {
  const [isPending, setIsPending] = useState(false);

  // Custom form submission handler
  const handleSubmit = async (event: React.FormEvent<HTMLButtonElement>) => {
    if (!formAction) return;

    // Only handle if we have a formAction
    event.preventDefault();

    const form = event.currentTarget.closest('form');
    if (!form) return;

    setIsPending(true);

    try {
      const formData = new FormData(form);
      await formAction(formData);
    } catch (error) {
      console.error("Form submission error:", error);
    } finally {
      setIsPending(false);
    }
  };

  return (
    <Button
      type="submit"
      aria-disabled={isPending}
      disabled={isPending}
      onClick={formAction ? handleSubmit : undefined}
      {...props}
    >
      {isPending ? pendingText : children}
    </Button>
  );
}
