# PrimeAI - Financial Chat Assistant

<PERSON><PERSON><PERSON> de chat inteligente para análise financeira com integração ao Google Vertex AI e visualização de gráficos ECharts.

## 🚀 Funcionalidades

- **Chat Inteligente**: Interface de conversação com agente financeiro
- **Gráficos Interativos**: Visualização de dados financeiros com ECharts
- **Autenticação**: Sistema completo de login/registro com Supabase
- **Sessões**: Gerenciamento de conversas e histórico
- **Responsivo**: Interface adaptada para desktop e mobile

## 🛠️ Tecnologias

- **Frontend**: Next.js 14, React 18, TypeScript
- **Backend**: Supabase (Auth + Database)
- **IA**: Google Vertex AI / Gemini
- **Gráficos**: ECharts
- **Styling**: Tailwind CSS + shadcn/ui
- **Deploy**: Vercel

## 📋 Pré-requisitos

- Node.js 18+
- Conta Supabase
- Conta Google Cloud (para Vertex AI)
- Conta Vercel (para deploy)

## 🔧 Configuração Local

### 1. <PERSON><PERSON> o repositório
```bash
git clone <repository-url>
cd primeai
```

### 2. Instale as dependências
```bash
npm install
```

### 3. Configure as variáveis de ambiente
Crie um arquivo `.env.local`:

```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Google Cloud / Vertex AI
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json
GOOGLE_CLOUD_PROJECT_ID=your_project_id

# Backend do Agente (se usando servidor Python separado)
NEXT_PUBLIC_AGENT_API_URL=http://localhost:8000
```

### 4. Execute o projeto
```bash
npm run dev
```

O projeto estará disponível em [http://localhost:3000](http://localhost:3000)

## 🚀 Deploy

### Vercel
1. Conecte seu repositório ao Vercel
2. Configure as variáveis de ambiente no dashboard
3. Deploy automático a cada push

### Variáveis de Ambiente para Produção
```env
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
NEXT_PUBLIC_AGENT_API_URL=
GOOGLE_APPLICATION_CREDENTIALS=
GOOGLE_CLOUD_PROJECT_ID=
```

## 📊 Formato de Gráficos

O sistema usa apenas **ECharts** para visualização. O agente deve retornar gráficos no formato:

```json
{
  "title": {"text": "Título do Gráfico"},
  "tooltip": {"trigger": "axis"},
  "xAxis": {"type": "category", "data": ["A", "B", "C"]},
  "yAxis": {"type": "value"},
  "series": [{
    "name": "Série 1",
    "type": "line",
    "data": [100, 200, 300]
  }]
}
```

### Tipos de Gráfico Suportados
- `line` - Gráfico de linha
- `bar` - Gráfico de barras
- `pie` - Gráfico de pizza
- `candlestick` - Gráfico candlestick
- `scatter` - Gráfico de dispersão
- Qualquer tipo suportado pelo ECharts

## 🏗️ Estrutura do Projeto

```
├── app/                    # App Router do Next.js
│   ├── (public)/          # Páginas públicas (auth)
│   ├── chat/              # Página principal do chat
│   └── api/               # API routes
├── components/
│   ├── chat/              # Componentes do chat
│   ├── auth/              # Componentes de autenticação
│   └── ui/                # Componentes UI (shadcn)
├── lib/                   # Utilitários e configurações
├── utils/                 # Utilitários do Supabase
└── types/                 # Definições de tipos TypeScript
```

## 🔐 Autenticação

O sistema usa Supabase Auth com:
- Login/Registro por email
- Recuperação de senha
- Sessões persistentes
- Middleware de proteção de rotas

## 💬 Sistema de Chat

- **Sessões**: Cada conversa é uma sessão separada
- **Histórico**: Mensagens persistidas no Supabase
- **Streaming**: Respostas em tempo real (opcional)

## 📈 Integração com Agente IA

O frontend se conecta com um backend Python que usa:
- Google Vertex AI / Gemini
- Processamento de dados financeiros
- Geração de gráficos ECharts
- APIs de dados financeiros (B3, etc.)

## 🐛 Debug

- Verifique logs no console do navegador
- Monitore requisições na aba Network
- Use ferramentas de desenvolvimento do navegador

## 📝 Licença

Este projeto é privado e proprietário.

## 🤝 Suporte

Para suporte técnico, entre em contato com a equipe de desenvolvimento.
