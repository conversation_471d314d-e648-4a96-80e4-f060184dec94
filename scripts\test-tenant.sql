-- =====================================================
-- SCRIPT DE TESTE PARA VERIFICAR CONFIGURAÇÃO MULTI-TENANT
-- Execute no Supabase SQL Editor
-- =====================================================

-- 1. Verificar se a tabela tenants existe no schema app
SELECT EXISTS (
   SELECT FROM information_schema.tables 
   WHERE table_schema = 'app' 
   AND table_name = 'tenants'
) as tabela_tenants_existe;

-- 2. Verificar tenants existentes
SELECT 
    id,
    subdomain,
    name,
    is_active,
    created_at
FROM app.tenants
ORDER BY created_at DESC;

-- 3. Criar tenant demo se não existir
INSERT INTO app.tenants (subdomain, name, primary_color, secondary_color, settings)
VALUES (
  'demo',
  'Empresa Demo',
  '#1f2937',
  '#f3f4f6',
  '{"features": {"chat": true, "analytics": true}}'
) ON CONFLICT (subdomain) DO NOTHING;

-- 4. Verificar se RLS está habilitado
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE schemaname = 'app' 
AND tablename IN ('tenants', 'agent_interaction_costs')
ORDER BY tablename;

-- 5. Verificar políticas RLS
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE schemaname = 'app'
ORDER BY tablename, policyname;

-- 6. Testar função get_current_tenant_id
SELECT get_current_tenant_id() as current_tenant_id;

-- 7. Verificar se tenant demo está ativo
SELECT 
    id,
    subdomain,
    name,
    is_active
FROM app.tenants 
WHERE subdomain = 'demo' 
AND is_active = true;

-- =====================================================
-- RESULTADO ESPERADO:
-- - tabela_tenants_existe: true
-- - Pelo menos 1 tenant 'demo' ativo
-- - RLS habilitado nas tabelas
-- - Políticas criadas
-- =====================================================
