import { Message } from "@/components/form-message";
import ForgotPasswordFormClient from "@/components/auth/forgot-password-form-client";
import { ForgotPasswordFormStatic } from "@/components/auth/static-forms";
import LayoutAuth from "../layout-auth";
import { MobileSafe } from "@/components/ui/mobile-safe";

export default async function ForgotPassword(props: {
  searchParams: Promise<Message>;
}) {
  const searchParams = await props.searchParams;
  return (
    <LayoutAuth>
      <MobileSafe fallback={<ForgotPasswordFormStatic />}>
        <ForgotPasswordFormClient initialMessage={searchParams} />
      </MobileSafe>
    </LayoutAuth>
  );
}
