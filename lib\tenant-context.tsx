'use client';

import { createContext, useContext, useEffect, useState } from 'react';

interface TenantData {
  id: string;
  subdomain: string;
  name: string;
  logoUrl?: string;
  primaryColor: string;
  secondaryColor: string;
  customCss?: string;
  settings: Record<string, any>;
}

interface TenantContextType {
  tenant: TenantData | null;
  isLoading: boolean;
  error: string | null;
}

const TenantContext = createContext<TenantContextType>({
  tenant: null,
  isLoading: true,
  error: null
});

export function TenantProvider({ children }: { children: React.ReactNode }) {
  const [tenant, setTenant] = useState<TenantData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadTenant() {
      try {
        // Verificar se estamos em um subdomínio
        const hostname = window.location.hostname;
        const isSubdomain = hostname.includes('.') && 
                           !hostname.startsWith('www.') && 
                           (hostname.includes('vascofa.shop') || hostname.includes('localhost'));

        if (!isSubdomain) {
          // Não é um subdomínio, não há tenant
          setIsLoading(false);
          return;
        }

        const response = await fetch('/api/tenant/current');
        if (response.ok) {
          const tenantData = await response.json();
          setTenant(tenantData);
        } else if (response.status === 404) {
          setError('Tenant não encontrado');
        } else {
          setError('Erro ao carregar dados do tenant');
        }
      } catch (error) {
        console.error('Erro ao carregar dados do tenant:', error);
        setError('Erro de conexão');
      } finally {
        setIsLoading(false);
      }
    }

    loadTenant();
  }, []);

  // Aplicar estilos do tenant quando carregado
  useEffect(() => {
    if (tenant) {
      // Aplicar cores personalizadas via CSS variables
      document.documentElement.style.setProperty('--tenant-primary', tenant.primaryColor);
      document.documentElement.style.setProperty('--tenant-secondary', tenant.secondaryColor);
      
      // Aplicar CSS customizado
      if (tenant.customCss) {
        const styleElement = document.createElement('style');
        styleElement.textContent = tenant.customCss;
        styleElement.id = 'tenant-custom-styles';
        
        // Remover estilos anteriores
        const existingStyles = document.getElementById('tenant-custom-styles');
        if (existingStyles) {
          existingStyles.remove();
        }
        
        document.head.appendChild(styleElement);
      }

      // Atualizar título da página
      document.title = `${tenant.name} - PrimeAI`;
    }
  }, [tenant]);

  return (
    <TenantContext.Provider value={{ tenant, isLoading, error }}>
      {children}
    </TenantContext.Provider>
  );
}

export const useTenant = () => {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error('useTenant deve ser usado dentro de um TenantProvider');
  }
  return context;
};
