import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

// GET - Listar todos os tenants
export async function GET() {
  try {
    const supabase = await createClient();
    
    const { data: tenants, error } = await supabase
      .from('tenants')
      .select('id, subdomain, name, is_active, created_at, primary_color, secondary_color')
      .order('created_at', { ascending: false });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(tenants);
  } catch (error) {
    console.error('Erro ao buscar tenants:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// POST - Criar novo tenant
export async function POST(request: NextRequest) {
  try {
    const { subdomain, name, primaryColor, secondaryColor } = await request.json();
    
    // Validações
    if (!subdomain || !name) {
      return NextResponse.json({ 
        error: 'Subdomínio e nome são obrigatórios' 
      }, { status: 400 });
    }

    // Validar formato do subdomínio
    if (!/^[a-z0-9-]+$/.test(subdomain)) {
      return NextResponse.json({ 
        error: 'Subdomínio deve conter apenas letras minúsculas, números e hífens' 
      }, { status: 400 });
    }

    // Verificar se subdomínio já existe
    const supabase = await createClient();
    
    const { data: existingTenant } = await supabase
      .from('tenants')
      .select('id')
      .eq('subdomain', subdomain)
      .single();

    if (existingTenant) {
      return NextResponse.json({ 
        error: 'Subdomínio já existe' 
      }, { status: 409 });
    }

    // Criar novo tenant
    const { data: tenant, error } = await supabase
      .from('tenants')
      .insert({
        subdomain,
        name,
        primary_color: primaryColor || '#1f2937',
        secondary_color: secondaryColor || '#f3f4f6',
        settings: {
          features: {
            chat: true,
            analytics: true
          }
        }
      })
      .select()
      .single();

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(tenant, { status: 201 });
  } catch (error) {
    console.error('Erro ao criar tenant:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}
