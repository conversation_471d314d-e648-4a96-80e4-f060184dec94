"use client";

import { useRef } from "react";
import { Button } from "@/components/ui/button";
import { X, Paperclip, FileText } from "lucide-react";
import { FileAttachment } from "./types";

interface FileUploadProps {
  selectedFiles: FileAttachment[];
  onFilesSelected: (files: FileAttachment[]) => void;
  onFileRemoved: (index: number) => void;
  disabled?: boolean;
}

export default function FileUpload({
  selectedFiles,
  onFilesSelected,
  onFileRemoved,
  disabled = false
}: FileUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    const newFiles: FileAttachment[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const url = URL.createObjectURL(file);
      
      newFiles.push({
        file,
        url,
        mimeType: file.type
      });
    }

    onFilesSelected([...selectedFiles, ...newFiles]);
    
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeFile = (index: number) => {
    // Revoke object URL to prevent memory leaks
    URL.revokeObjectURL(selectedFiles[index].url);
    onFileRemoved(index);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-2">
      {/* File Input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept="image/*,.pdf,.doc,.docx,.txt,.csv,.xlsx"
        onChange={handleFileSelect}
        className="hidden"
        disabled={disabled}
      />
      
      {/* Upload Button */}
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={() => fileInputRef.current?.click()}
        disabled={disabled}
        className="flex items-center gap-2"
      >
        <Paperclip className="h-4 w-4" />
        Anexar arquivo
      </Button>

      {/* File Previews */}
      {selectedFiles.length > 0 && (
        <div className="flex flex-wrap gap-2 p-2 border rounded-md bg-muted/50">
          {selectedFiles.map((fileAttachment, index) => (
            <div
              key={index}
              className="relative flex items-center gap-2 p-2 border rounded bg-background"
            >
              {fileAttachment.file.type.startsWith('image/') ? (
                <div className="relative">
                  <img
                    src={fileAttachment.url}
                    alt="Preview"
                    className="w-12 h-12 object-cover rounded"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    className="absolute -top-2 -right-2 h-5 w-5 p-0"
                    onClick={() => removeFile(index)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <FileText className="h-8 w-8 text-muted-foreground" />
                  <div className="flex flex-col">
                    <span className="text-sm font-medium truncate max-w-[100px]">
                      {fileAttachment.file.name}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {formatFileSize(fileAttachment.file.size)}
                    </span>
                  </div>
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    className="h-5 w-5 p-0"
                    onClick={() => removeFile(index)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
