export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      cotacoes_historicas_b3: {
        Row: {
          codbdi: string | null
          codisi: string | null
          codneg: string
          created_at: string | null
          data_pregao: string
          datven: string | null
          dismes: number | null
          especi: string | null
          fatcot: number | null
          id: number
          indopc: number | null
          modref: string | null
          nomres: string | null
          prazot: string | null
          preabe: number | null
          preexe: number | null
          premax: number | null
          premed: number | null
          premin: number | null
          preofc: number | null
          preofv: number | null
          preult: number | null
          ptoexe: number | null
          quatot: number | null
          tipo_registro: string
          totneg: number | null
          tpmerc: number | null
          voltot: number | null
        }
        Insert: {
          codbdi?: string | null
          codisi?: string | null
          codneg: string
          created_at?: string | null
          data_pregao: string
          datven?: string | null
          dismes?: number | null
          especi?: string | null
          fatcot?: number | null
          id?: never
          indopc?: number | null
          modref?: string | null
          nomres?: string | null
          prazot?: string | null
          preabe?: number | null
          preexe?: number | null
          premax?: number | null
          premed?: number | null
          premin?: number | null
          preofc?: number | null
          preofv?: number | null
          preult?: number | null
          ptoexe?: number | null
          quatot?: number | null
          tipo_registro: string
          totneg?: number | null
          tpmerc?: number | null
          voltot?: number | null
        }
        Update: {
          codbdi?: string | null
          codisi?: string | null
          codneg?: string
          created_at?: string | null
          data_pregao?: string
          datven?: string | null
          dismes?: number | null
          especi?: string | null
          fatcot?: number | null
          id?: never
          indopc?: number | null
          modref?: string | null
          nomres?: string | null
          prazot?: string | null
          preabe?: number | null
          preexe?: number | null
          premax?: number | null
          premed?: number | null
          premin?: number | null
          preofc?: number | null
          preofv?: number | null
          preult?: number | null
          ptoexe?: number | null
          quatot?: number | null
          tipo_registro?: string
          totneg?: number | null
          tpmerc?: number | null
          voltot?: number | null
        }
        Relationships: []
      }
      cri_cra_precos: {
        Row: {
          codigo: string | null
          created_at: string | null
          data_referencia: string | null
          desvio_padrao: number | null
          duration: number | null
          emissao: string | null
          emissor: string | null
          id: number
          indice_correcao: string | null
          pu: number | null
          pu_par: number | null
          referencia_ntnb: string | null
          reune: number | null
          risco_credito: string | null
          serie: string | null
          taxa_compra: number | null
          taxa_indicativa: number | null
          taxa_venda: number | null
          vencimento: string | null
        }
        Insert: {
          codigo?: string | null
          created_at?: string | null
          data_referencia?: string | null
          desvio_padrao?: number | null
          duration?: number | null
          emissao?: string | null
          emissor?: string | null
          id?: never
          indice_correcao?: string | null
          pu?: number | null
          pu_par?: number | null
          referencia_ntnb?: string | null
          reune?: number | null
          risco_credito?: string | null
          serie?: string | null
          taxa_compra?: number | null
          taxa_indicativa?: number | null
          taxa_venda?: number | null
          vencimento?: string | null
        }
        Update: {
          codigo?: string | null
          created_at?: string | null
          data_referencia?: string | null
          desvio_padrao?: number | null
          duration?: number | null
          emissao?: string | null
          emissor?: string | null
          id?: never
          indice_correcao?: string | null
          pu?: number | null
          pu_par?: number | null
          referencia_ntnb?: string | null
          reune?: number | null
          risco_credito?: string | null
          serie?: string | null
          taxa_compra?: number | null
          taxa_indicativa?: number | null
          taxa_venda?: number | null
          vencimento?: string | null
        }
        Relationships: []
      }
      curvas_b3: {
        Row: {
          caracteristica_vertice: string | null
          cod_curvas_termo: string | null
          cod_taxa: string | null
          cod_vertice: string | null
          complemento_transacao: string | null
          created_at: string | null
          desc_taxa: string | null
          dias_corridos_taxa_juro: number | null
          dt_geracao_arquivo: string
          id: number
          id_transacao: string
          saques_taxa_juro: number | null
          sinal_taxa_teorica: string | null
          taxa_teorica: number | null
          tipo_registro: string | null
        }
        Insert: {
          caracteristica_vertice?: string | null
          cod_curvas_termo?: string | null
          cod_taxa?: string | null
          cod_vertice?: string | null
          complemento_transacao?: string | null
          created_at?: string | null
          desc_taxa?: string | null
          dias_corridos_taxa_juro?: number | null
          dt_geracao_arquivo: string
          id?: number
          id_transacao: string
          saques_taxa_juro?: number | null
          sinal_taxa_teorica?: string | null
          taxa_teorica?: number | null
          tipo_registro?: string | null
        }
        Update: {
          caracteristica_vertice?: string | null
          cod_curvas_termo?: string | null
          cod_taxa?: string | null
          cod_vertice?: string | null
          complemento_transacao?: string | null
          created_at?: string | null
          desc_taxa?: string | null
          dias_corridos_taxa_juro?: number | null
          dt_geracao_arquivo?: string
          id?: number
          id_transacao?: string
          saques_taxa_juro?: number | null
          sinal_taxa_teorica?: string | null
          taxa_teorica?: number | null
          tipo_registro?: string | null
        }
        Relationships: []
      }
      cvm_informes_diarios_fi: {
        Row: {
          captc_dia: number | null
          cnpj_fundo: string
          created_at: string | null
          dt_comptc: string
          id: number
          id_subclasse: string | null
          nr_cotst: number | null
          resg_dia: number | null
          tp_fundo: string | null
          vl_patrim_liq: number | null
          vl_quota: number | null
          vl_total: number | null
        }
        Insert: {
          captc_dia?: number | null
          cnpj_fundo: string
          created_at?: string | null
          dt_comptc: string
          id?: number
          id_subclasse?: string | null
          nr_cotst?: number | null
          resg_dia?: number | null
          tp_fundo?: string | null
          vl_patrim_liq?: number | null
          vl_quota?: number | null
          vl_total?: number | null
        }
        Update: {
          captc_dia?: number | null
          cnpj_fundo?: string
          created_at?: string | null
          dt_comptc?: string
          id?: number
          id_subclasse?: string | null
          nr_cotst?: number | null
          resg_dia?: number | null
          tp_fundo?: string | null
          vl_patrim_liq?: number | null
          vl_quota?: number | null
          vl_total?: number | null
        }
        Relationships: []
      }
      debentures_precos: {
        Row: {
          codigo: string | null
          created_at: string | null
          data_referencia: string
          desvio_padrao: number | null
          duration: number | null
          id: number
          indice_correcao: string | null
          intervalo_indicativo_maximo: number | null
          intervalo_indicativo_minimo: number | null
          nome: string | null
          pu: number | null
          pu_par: number | null
          referencia_ntn_b: string | null
          reune: number | null
          taxa_compra: number | null
          taxa_indicativa: number | null
          taxa_venda: number | null
          vencimento: string | null
        }
        Insert: {
          codigo?: string | null
          created_at?: string | null
          data_referencia: string
          desvio_padrao?: number | null
          duration?: number | null
          id?: number
          indice_correcao?: string | null
          intervalo_indicativo_maximo?: number | null
          intervalo_indicativo_minimo?: number | null
          nome?: string | null
          pu?: number | null
          pu_par?: number | null
          referencia_ntn_b?: string | null
          reune?: number | null
          taxa_compra?: number | null
          taxa_indicativa?: number | null
          taxa_venda?: number | null
          vencimento?: string | null
        }
        Update: {
          codigo?: string | null
          created_at?: string | null
          data_referencia?: string
          desvio_padrao?: number | null
          duration?: number | null
          id?: number
          indice_correcao?: string | null
          intervalo_indicativo_maximo?: number | null
          intervalo_indicativo_minimo?: number | null
          nome?: string | null
          pu?: number | null
          pu_par?: number | null
          referencia_ntn_b?: string | null
          reune?: number | null
          taxa_compra?: number | null
          taxa_indicativa?: number | null
          taxa_venda?: number | null
          vencimento?: string | null
        }
        Relationships: []
      }
      letras_precos: {
        Row: {
          cnpj: string | null
          created_at: string | null
          data_referencia: string
          doze_meses: number | null
          emissor: string | null
          fluxo: string | null
          id: number
          indexador: string | null
          letra: string | null
          oitenta_e_quatro_meses: number | null
          quarenta_e_oito_meses: number | null
          seis_meses: number | null
          sessenta_meses: number | null
          setenta_e_dois_meses: number | null
          taxa: string | null
          tres_meses: number | null
          trinta_seis_meses: number | null
          umes: number | null
          vinte_quatro_meses: number | null
        }
        Insert: {
          cnpj?: string | null
          created_at?: string | null
          data_referencia: string
          doze_meses?: number | null
          emissor?: string | null
          fluxo?: string | null
          id?: never
          indexador?: string | null
          letra?: string | null
          oitenta_e_quatro_meses?: number | null
          quarenta_e_oito_meses?: number | null
          seis_meses?: number | null
          sessenta_meses?: number | null
          setenta_e_dois_meses?: number | null
          taxa?: string | null
          tres_meses?: number | null
          trinta_seis_meses?: number | null
          umes?: number | null
          vinte_quatro_meses?: number | null
        }
        Update: {
          cnpj?: string | null
          created_at?: string | null
          data_referencia?: string
          doze_meses?: number | null
          emissor?: string | null
          fluxo?: string | null
          id?: never
          indexador?: string | null
          letra?: string | null
          oitenta_e_quatro_meses?: number | null
          quarenta_e_oito_meses?: number | null
          seis_meses?: number | null
          sessenta_meses?: number | null
          setenta_e_dois_meses?: number | null
          taxa?: string | null
          tres_meses?: number | null
          trinta_seis_meses?: number | null
          umes?: number | null
          vinte_quatro_meses?: number | null
        }
        Relationships: []
      }
      titulos_publicos_precos: {
        Row: {
          codigo_selic: string | null
          created_at: string | null
          criterio: string | null
          data_base_emissao: string | null
          data_referencia: string | null
          data_vencimento: string | null
          desvio_padrao: number | null
          id: number
          interv_ind_inf_d0: number | null
          interv_ind_inf_d1: number | null
          interv_ind_sup_d0: number | null
          interv_ind_sup_d1: number | null
          pu: number | null
          titulo: string | null
          tx_compra: number | null
          tx_indicativas: number | null
          tx_venda: number | null
        }
        Insert: {
          codigo_selic?: string | null
          created_at?: string | null
          criterio?: string | null
          data_base_emissao?: string | null
          data_referencia?: string | null
          data_vencimento?: string | null
          desvio_padrao?: number | null
          id?: number
          interv_ind_inf_d0?: number | null
          interv_ind_inf_d1?: number | null
          interv_ind_sup_d0?: number | null
          interv_ind_sup_d1?: number | null
          pu?: number | null
          titulo?: string | null
          tx_compra?: number | null
          tx_indicativas?: number | null
          tx_venda?: number | null
        }
        Update: {
          codigo_selic?: string | null
          created_at?: string | null
          criterio?: string | null
          data_base_emissao?: string | null
          data_referencia?: string | null
          data_vencimento?: string | null
          desvio_padrao?: number | null
          id?: number
          interv_ind_inf_d0?: number | null
          interv_ind_inf_d1?: number | null
          interv_ind_sup_d0?: number | null
          interv_ind_sup_d1?: number | null
          pu?: number | null
          titulo?: string | null
          tx_compra?: number | null
          tx_indicativas?: number | null
          tx_venda?: number | null
        }
        Relationships: []
      }
    }
    Views: {
      titulos_publicos: {
        Row: {
          codigo_selic: string | null
          criterio: string | null
          data_base_emissao: string | null
          data_referencia: string | null
          data_vencimento: string | null
          desvio_padrao: number | null
          interv_ind_inf_d0: number | null
          interv_ind_inf_d1: number | null
          interv_ind_sup_d0: number | null
          interv_ind_sup_d1: number | null
          pu: number | null
          titulo: string | null
          tx_compra: number | null
          tx_indicativas: number | null
          tx_venda: number | null
        }
        Insert: {
          codigo_selic?: string | null
          criterio?: string | null
          data_base_emissao?: string | null
          data_referencia?: string | null
          data_vencimento?: string | null
          desvio_padrao?: number | null
          interv_ind_inf_d0?: number | null
          interv_ind_inf_d1?: number | null
          interv_ind_sup_d0?: number | null
          interv_ind_sup_d1?: number | null
          pu?: number | null
          titulo?: string | null
          tx_compra?: number | null
          tx_indicativas?: number | null
          tx_venda?: number | null
        }
        Update: {
          codigo_selic?: string | null
          criterio?: string | null
          data_base_emissao?: string | null
          data_referencia?: string | null
          data_vencimento?: string | null
          desvio_padrao?: number | null
          interv_ind_inf_d0?: number | null
          interv_ind_inf_d1?: number | null
          interv_ind_sup_d0?: number | null
          interv_ind_sup_d1?: number | null
          pu?: number | null
          titulo?: string | null
          tx_compra?: number | null
          tx_indicativas?: number | null
          tx_venda?: number | null
        }
        Relationships: []
      }
    }
    Functions: {
      bytea_to_text: {
        Args: { data: string }
        Returns: string
      }
      http: {
        Args: { request: Database["public"]["CompositeTypes"]["http_request"] }
        Returns: Database["public"]["CompositeTypes"]["http_response"]
      }
      http_delete: {
        Args:
          | { uri: string }
          | { uri: string; content: string; content_type: string }
        Returns: Database["public"]["CompositeTypes"]["http_response"]
      }
      http_get: {
        Args: { uri: string } | { uri: string; data: Json }
        Returns: Database["public"]["CompositeTypes"]["http_response"]
      }
      http_head: {
        Args: { uri: string }
        Returns: Database["public"]["CompositeTypes"]["http_response"]
      }
      http_header: {
        Args: { field: string; value: string }
        Returns: Database["public"]["CompositeTypes"]["http_header"]
      }
      http_list_curlopt: {
        Args: Record<PropertyKey, never>
        Returns: {
          curlopt: string
          value: string
        }[]
      }
      http_patch: {
        Args: { uri: string; content: string; content_type: string }
        Returns: Database["public"]["CompositeTypes"]["http_response"]
      }
      http_post: {
        Args:
          | { uri: string; content: string; content_type: string }
          | { uri: string; data: Json }
        Returns: Database["public"]["CompositeTypes"]["http_response"]
      }
      http_put: {
        Args: { uri: string; content: string; content_type: string }
        Returns: Database["public"]["CompositeTypes"]["http_response"]
      }
      http_reset_curlopt: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      http_set_curlopt: {
        Args: { curlopt: string; value: string }
        Returns: boolean
      }
      text_to_bytea: {
        Args: { data: string }
        Returns: string
      }
      urlencode: {
        Args: { data: Json } | { string: string } | { string: string }
        Returns: string
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      http_header: {
        field: string | null
        value: string | null
      }
      http_request: {
        method: unknown | null
        uri: string | null
        headers: Database["public"]["CompositeTypes"]["http_header"][] | null
        content_type: string | null
        content: string | null
      }
      http_response: {
        status: number | null
        content_type: string | null
        headers: Database["public"]["CompositeTypes"]["http_header"][] | null
        content: string | null
      }
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const 