"use client";

import { useEffect, useState } from "react";

interface ClientOnlyProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * Componente baseado na documentação oficial do Next.js
 * Implementa a estratégia recomendada para evitar problemas de hidratação
 * Ref: https://nextjs.org/docs/messages/react-hydration-error
 */
export function ClientOnly({ children, fallback = null }: ClientOnlyProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return <>{isClient ? children : fallback}</>;
}
