# 🔧 CORREÇÃO: REGISTRO DE USUÁRIOS COM TENANT_ID

## ❌ Problema Identificado
```
ERROR: null value in column "tentant_id" of relation "users" violates not-null constraint
```

O erro ocorre porque:
1. A tabela `app.users` tem constraint NOT NULL no `tenant_id`
2. A função trigger não estava incluindo o `tenant_id` no registro
3. O sistema de custos não estava associando registros ao tenant

## ✅ Correções Aplicadas

### 1. **Script SQL Completo** (`sql/fix-user-registration.sql`)
- Corrige estrutura da tabela `app.users`
- Atualiza função `handle_new_user()` para incluir `tenant_id`
- Adiciona `tenant_id` à tabela `app.agent_interaction_costs`
- Cria funções auxiliares para gerenciamento de tenant

### 2. **Função de Signup Atualizada** (`app/actions.ts`)
- Detecta subdomínio durante o registro
- Inclui subdomínio no metadata do usuário
- Suporte para localhost e produção

### 3. **Agent Cost Logger Atualizado** (`lib/agent-cost-logger.ts`)
- Busca `tenant_id` do usuário automaticamente
- Inclui `tenant_id` nos registros de custo
- Interface atualizada

## 🚀 Como Aplicar as Correções

### 1. Execute o Script SQL
```sql
-- No Supabase SQL Editor, execute TODO o conteúdo de:
-- sql/fix-user-registration.sql
```

### 2. Teste o Registro
1. Acesse `http://demo.localhost:3000/sign-up`
2. Registre um novo usuário
3. Verifique se não há mais erros

### 3. Verificar Resultados
```sql
-- Verificar usuários com tenant_id
SELECT user_id, full_name, email, tenant_id FROM app.users;

-- Verificar tenant do usuário
SELECT u.full_name, u.email, t.name as tenant_name, t.subdomain
FROM app.users u
JOIN app.tenants t ON u.tenant_id = t.id;
```

## 🔍 Fluxo Corrigido

### Registro de Usuário:
1. **Frontend**: Usuário acessa `demo.localhost:3000/sign-up`
2. **Middleware**: Detecta subdomínio "demo"
3. **Signup Action**: Inclui `subdomain: "demo"` no metadata
4. **Supabase Auth**: Cria usuário com metadata
5. **Trigger**: `handle_new_user()` busca tenant pelo subdomínio
6. **Database**: Insere em `app.users` com `tenant_id` correto

### Registro de Custos:
1. **Agent Response**: Gera resposta com custos
2. **Cost Logger**: Busca `tenant_id` do usuário
3. **Database**: Insere em `app.agent_interaction_costs` com `tenant_id`

## 📋 Verificações Pós-Correção

### ✅ Checklist:
- [ ] Script SQL executado sem erros
- [ ] Função `handle_new_user()` atualizada
- [ ] Trigger recriado
- [ ] Usuários existentes têm `tenant_id`
- [ ] Novo registro funciona sem erros
- [ ] Custos incluem `tenant_id`

### 🧪 Testes:
```bash
# 1. Registrar novo usuário
curl -X POST http://demo.localhost:3000/signup \
  -d "email=<EMAIL>&password=123456&full_name=Test User"

# 2. Verificar no banco
SELECT * FROM app.users WHERE email = '<EMAIL>';

# 3. Testar chat e verificar custos
# (usar a interface normalmente)
```

## 🔧 Funções Criadas/Atualizadas

### `handle_new_user()`
- Busca `tenant_id` do contexto RLS
- Fallback para metadata do usuário
- Fallback para tenant demo
- Insere usuário com `tenant_id`

### `get_tenant_from_subdomain(TEXT)`
- Busca tenant pelo subdomínio
- Retorna UUID do tenant
- Valida se tenant está ativo

### `insert_agent_cost(...)`
- Função auxiliar para inserir custos
- Inclui `tenant_id` automaticamente
- Busca tenant do usuário

## 🚨 Pontos de Atenção

### 1. **Usuários Existentes**
- Script atualiza usuários sem `tenant_id` para tenant "demo"
- Verificar se isso está correto para seu caso

### 2. **Constraint NOT NULL**
- Removida temporariamente para permitir migração
- Pode ser reativada após correções

### 3. **Fallbacks**
- Sistema tem múltiplos fallbacks para garantir `tenant_id`
- Ordem: RLS → Metadata → Demo → Primeiro ativo

## 📊 Monitoramento

### Logs a Observar:
```bash
# Sucesso no registro
"Database user created successfully"

# Sucesso no cost logging
"Custo registrado com sucesso"

# Sem erros de constraint
# (não deve aparecer mais o erro de tenant_id null)
```

### Queries de Monitoramento:
```sql
-- Usuários sem tenant
SELECT COUNT(*) FROM app.users WHERE tenant_id IS NULL;

-- Custos sem tenant
SELECT COUNT(*) FROM app.agent_interaction_costs WHERE tenant_id IS NULL;

-- Distribuição por tenant
SELECT t.name, COUNT(u.user_id) as users_count
FROM app.tenants t
LEFT JOIN app.users u ON t.id = u.tenant_id
GROUP BY t.id, t.name;
```

---

## ✅ RESULTADO ESPERADO

Após aplicar as correções:
- ✅ Registro de usuários funciona sem erros
- ✅ Todos os usuários têm `tenant_id` válido
- ✅ Custos são associados ao tenant correto
- ✅ Isolamento de dados mantido
- ✅ Sistema multi-tenant totalmente funcional
