"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Badge } from "./ui/badge";
import { Button } from "./ui/button";
import { createClient } from "@/utils/supabase/client";
import { AppConfig } from "@/lib/config/app-config";
import { signOutClient } from "@/app/client-actions";

export default function HeaderAuthClient() {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [hasEnvVars, setHasEnvVars] = useState(true);
  const router = useRouter();
  const supabase = createClient();

  // Verificar se signup está habilitado
  const isSignupEnabled = AppConfig.enableSignup;

  useEffect(() => {
    // Check if environment variables are set
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      setHasEnvVars(false);
    }

    // Get user data
    const getUser = async () => {
      try {
        const { data, error } = await supabase.auth.getUser();
        if (error) {
          // Erro de autenticação é normal quando não há usuário logado
          setUser(null);
        } else {
          setUser(data.user);
        }
      } catch (error) {
        // Erro na verificação é tratado silenciosamente
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    getUser();

    // Set up auth state change listener
    const { data: authListener } = supabase.auth.onAuthStateChange(
      (event, session) => {
        if (event === "SIGNED_IN" && session) {
          setUser(session.user);
        } else if (event === "SIGNED_OUT") {
          setUser(null);
        }
      }
    );

    return () => {
      // Clean up the subscription
      authListener.subscription.unsubscribe();
    };
  }, [supabase.auth]);

  const handleSignOut = async () => {
    try {
      const result = await signOutClient();
      if (result.success) {
        // Forçar a atualização do estado local
        setUser(null);
        // Navegar para a página de login
        router.push("/sign-in");
      }
    } catch (error) {
      // Erro no logout é tratado silenciosamente
    }
  };

  if (loading) {
    return <div className="h-8 w-24 animate-pulse rounded bg-muted"></div>;
  }

  if (!hasEnvVars) {
    return (
      <>
        <div className="flex gap-4 items-center">
          <div>
            <Badge
              variant={"default"}
              className="font-normal pointer-events-none"
            >
              Please update .env.local file with anon key and url
            </Badge>
          </div>
          <div className="flex gap-2">
            <Button
              asChild
              size="sm"
              variant={"outline"}
              disabled
              className="opacity-75 cursor-none pointer-events-none"
            >
              <Link href="/sign-in">Sign in</Link>
            </Button>
            {isSignupEnabled && (
              <Button
                asChild
                size="sm"
                variant={"default"}
                disabled
                className="opacity-75 cursor-none pointer-events-none"
              >
                <Link href="/sign-up">Sign up</Link>
              </Button>
            )}
          </div>
        </div>
      </>
    );
  }

  return user ? (
    <div className="flex items-center gap-4">
      Hey, {user.email}!
      <Button onClick={handleSignOut} type="button" variant={"outline"}>
        Sign out
      </Button>
    </div>
  ) : (
    <div className="flex gap-2">
      <Button asChild size="sm" variant={"outline"}>
        <Link href="/sign-in">Sign in</Link>
      </Button>
      {isSignupEnabled && (
        <Button asChild size="sm" variant={"default"}>
          <Link href="/sign-up">Sign up</Link>
        </Button>
      )}
    </div>
  );
}
