import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

// Definindo os headers CORS como constante (não exportada)
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, apikey'
};

export async function OPTIONS() {
  return NextResponse.json({}, { headers: corsHeaders });
}

export async function GET(request: NextRequest) {
  // Verificar autenticação
  const authHeader = request.headers.get('Authorization');

  if (!authHeader) {
    return NextResponse.json(
      { error: 'Cabeçalho de autorização não fornecido' },
      { status: 401, headers: corsHeaders }
    );
  }

  const token = authHeader.replace('Bearer ', '');
  const supabase = await createClient();

  // Verificar o usuário
  const { data: { user }, error: userError } = await supabase.auth.getUser(token);

  if (userError || !user) {
    return NextResponse.json(
      { error: 'Token inválido' },
      { status: 401, headers: corsHeaders }
    );
  }

  // Obter o parâmetro data_referencia, se fornecido
  const url = new URL(request.url);
  const dataReferencia = url.searchParams.get('data_referencia');

  // Construir a consulta para debentures_precos
  let query = supabase
    .from('debentures_precos')
    .select('codigo, data_referencia, desvio_padrao, duration, indice_correcao, intervalo_indicativo_maximo, intervalo_indicativo_minimo, nome, pu, pu_par, referencia_ntn_b, reune, taxa_compra, taxa_indicativa, taxa_venda, vencimento');

  // Adicionar filtro por data_referencia, se fornecido
  if (dataReferencia) {
    query = query.eq('data_referencia', dataReferencia);
  }

  const { data, error } = await query;

  if (error) {
    return NextResponse.json(
      { error: error.message },
      { status: 500, headers: corsHeaders }
    );
  }

  return NextResponse.json(
    { data },
    { headers: corsHeaders }
  );
}