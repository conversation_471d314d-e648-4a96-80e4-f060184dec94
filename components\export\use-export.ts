"use client";

import { useState, useCallback, useEffect } from "react";
import { ExportService } from "@/lib/services/export-service";
import { DATABASES } from "@/lib/config/database-config";
import { Database } from "@/types/database";
import { createClient } from "@/utils/supabase/client";
import { toast } from "sonner";

export function useExport() {
  // Estado para controlar o modal
  const [isOpen, setIsOpen] = useState(false);
  
  // Estado para armazenar quais bases estão selecionadas
  const [selectedDatabases, setSelectedDatabases] = useState<string[]>(
    DATABASES.map((db) => db.id)
  );
  
  // Estado para controlar o loading durante a exportação
  const [isExporting, setIsExporting] = useState(false);
  
  // Estado para armazenar o token de acesso
  const [accessToken, setAccessToken] = useState<string | null>(null);

  // Função para obter e armazenar o token de acesso
  const refreshAccessToken = useCallback(async () => {
    try {
      console.log("[useExport] Refreshing access token...");
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();
      
      if (session?.access_token) {
        console.log("[useExport] Token atualizado com sucesso!");
        setAccessToken(session.access_token);
      } else {
        console.warn("[useExport] Sessão ou token não encontrados");
        setAccessToken(null);
      }
    } catch (error) {
      console.error("[useExport] Erro ao obter token de acesso:", error);
      setAccessToken(null);
    }
  }, []);

  // Obter o token de acesso quando o componente for montado
  useEffect(() => {
    refreshAccessToken();
    
    // Atualizar o token a cada 5 minutos para garantir que esteja fresco
    const intervalId = setInterval(refreshAccessToken, 1000 * 60 * 5);
    
    return () => clearInterval(intervalId);
  }, [refreshAccessToken]);

  // Verifica se uma base está selecionada
  const isSelected = useCallback((id: string) => {
    return selectedDatabases.includes(id);
  }, [selectedDatabases]);

  // Alterna a seleção de uma base específica
  const toggleDatabase = useCallback((id: string) => {
    setSelectedDatabases(prev => {
      if (prev.includes(id)) {
        return prev.filter(dbId => dbId !== id);
      } else {
        return [...prev, id];
      }
    });
  }, []);

  // Verifica se todas as bases estão selecionadas
  const allSelected = selectedDatabases.length === DATABASES.length;

  // Alterna entre selecionar todas ou nenhuma
  const toggleSelectAll = useCallback(() => {
    if (allSelected) {
      setSelectedDatabases([]);
    } else {
      setSelectedDatabases(DATABASES.map(db => db.id));
    }
  }, [allSelected]);

  // Função para exportar os dados
  const exportData = useCallback(async (dataReferencia?: string) => {
    console.log("[useExport] Iniciando exportação, verificando token...");
    
    if (!accessToken) {
      console.error("[useExport] Token de acesso não disponível");
      toast.error("Erro de autenticação. Por favor, faça login novamente.");
      
      // Tentar obter o token novamente
      await refreshAccessToken();
      
      if (!accessToken) {
        return;
      }
    }
    
    setIsExporting(true);
    try {
      console.log("[useExport] Chamando serviço de exportação...");
      const success = await ExportService.exportToExcel(
        selectedDatabases,
        DATABASES,
        dataReferencia,
        accessToken // Passando o token explicitamente para o serviço
      );
      
      if (success) {
        setIsOpen(false);
      }
    } catch (error) {
      console.error("[useExport] Erro durante exportação:", error);
      toast.error("Falha na exportação. Por favor, tente novamente.");
    } finally {
      setIsExporting(false);
    }
  }, [selectedDatabases, accessToken, refreshAccessToken]);

  return {
    // Estado
    isOpen,
    selectedDatabases,
    isExporting,
    databases: DATABASES,
    allSelected,
    accessToken,
    
    // Ações
    setIsOpen,
    toggleDatabase,
    toggleSelectAll,
    exportData,
    refreshAccessToken,
  };
} 