"use client";

import React from "react";
import { useSession } from "./session-context";
import { useSidebar } from "./sidebar-context";
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle, MessageSquare, Trash2, ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from "@/components/ui/tooltip";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useIsMobile } from "@/components/hooks/use-mobile";

interface SessionSidebarProps {
  userId: string;
  hideFooter?: boolean; // Para ocultar o rodapé quando usado no MobileSidebar
}

export default function SessionSidebar({ userId, hideFooter = false }: SessionSidebarProps) {
  const { isExpanded, toggleSidebar } = useSidebar();
  const isMobile = useIsMobile();

  const {
    sessions,
    currentSessionId,
    isCreatingSession,
    createNewSession,
    deleteSession,
    setCurrentSessionId,
  } = useSession();

  const handleCreateSession = async () => {
    try {
      await createNewSession(userId);
    } catch (error) {
      console.error("Erro ao criar nova sessão:", error);
    }
  };

  const handleDeleteSession = async (sessionId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await deleteSession(userId, sessionId);
    } catch (error) {
      console.error("Erro ao deletar sessão:", error);
    }
  };

  return (
    <TooltipProvider>
      <div className="flex flex-col h-full bg-card w-full relative">
        {/* Cabeçalho */}
        <div className={cn(
          "border-b border-border py-3 flex-shrink-0 h-14 flex items-center",
          isMobile ? "px-4 justify-start" : (isExpanded ? "px-4 justify-between" : "px-2 justify-center")
        )}>
          {(isExpanded || isMobile) && <h2 className="text-lg font-semibold">Conversas</h2>}
          {!isMobile && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={toggleSidebar}
                >
                  {isExpanded ? (
                    <ChevronLeft className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {isExpanded ? "Recolher sidebar" : "Expandir sidebar"}
                  </span>
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">
                {isExpanded ? "Recolher sidebar" : "Expandir sidebar"}
              </TooltipContent>
            </Tooltip>
          )}
        </div>

        {/* Lista de conversas */}
        <div className={cn("flex-1 overflow-hidden", isMobile ? "pb-20" : "")}>
          <ScrollArea className="h-full">
            <div className="py-2">
              {sessions.length === 0 ? (
                (!isExpanded && !isMobile) ? null : (
                  <div className="px-4 py-3 text-sm text-muted-foreground">
                    Selecione ou crie uma conversa
                  </div>
                )
              ) : (
                sessions
                  .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
                  .map((session) => (
                    <div key={session.id}>
                      {(!isExpanded && !isMobile) ? (
                        // Desktop recolhido: Tooltip com nome da sessão
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div
                              className={cn(
                                "group flex items-center justify-center px-3 py-2 mx-2 rounded-md cursor-pointer hover:bg-muted",
                                currentSessionId === session.id && "bg-muted"
                              )}
                              onClick={() => setCurrentSessionId(session.id)}
                            >
                              <MessageSquare className="h-4 w-4 text-muted-foreground" />
                            </div>
                          </TooltipTrigger>
                          <TooltipContent side="right">
                            <div className="flex items-center gap-2">
                              <span>{session.name}</span>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-4 w-4"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteSession(session.id, e);
                                }}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      ) : (
                        // Desktop expandido e Mobile: Layout completo
                        <div
                          className={cn(
                            "group flex items-center justify-between px-3 py-2 mx-2 rounded-md cursor-pointer hover:bg-muted",
                            currentSessionId === session.id && "bg-muted"
                          )}
                          onClick={() => setCurrentSessionId(session.id)}
                        >
                          <div className="flex items-center gap-2 overflow-hidden">
                            <MessageSquare className="h-4 w-4 shrink-0 text-muted-foreground" />
                            <span className="text-sm truncate">{session.name}</span>
                          </div>
                          {!isMobile && (
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-6 w-6 opacity-0 group-hover:opacity-100"
                                  onClick={(e) => handleDeleteSession(session.id, e)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                  <span className="sr-only">Deletar conversa</span>
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>Deletar conversa</TooltipContent>
                            </Tooltip>
                          )}
                          {isMobile && (
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6 opacity-70"
                              onClick={(e) => handleDeleteSession(session.id, e)}
                            >
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">Deletar conversa</span>
                            </Button>
                          )}
                        </div>
                      )}
                    </div>
                  ))
              )}
            </div>
          </ScrollArea>
        </div>

        {/* Rodapé - Sempre visível (exceto quando hideFooter é true) */}
        {!hideFooter && (
          <div className={cn(
            "border-t border-border bg-background",
            isMobile
              ? "absolute bottom-0 left-0 right-0 p-4 pb-safe z-20"
              : (isExpanded ? "flex-shrink-0 p-4 safe-bottom" : "flex-shrink-0 p-2 safe-bottom")
          )}>
            {(isExpanded || isMobile) ? (
              <Button
                onClick={handleCreateSession}
                disabled={isCreatingSession}
                className="w-full justify-start min-h-[44px] font-medium"
                variant="outline"
              >
                <PlusCircle className="mr-2 h-4 w-4" />
                Nova conversa
              </Button>
            ) : (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    onClick={handleCreateSession}
                    disabled={isCreatingSession}
                    className="w-full"
                    variant="outline"
                    size="icon"
                  >
                    <PlusCircle className="h-4 w-4" />
                    <span className="sr-only">Nova conversa</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">Nova conversa</TooltipContent>
              </Tooltip>
            )}
          </div>
        )}
      </div>
    </TooltipProvider>
  );
}
