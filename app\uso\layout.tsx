"use client";

import { ReactNode } from "react";
import { createClient } from "@/utils/supabase/client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import MainSidebar from "@/components/main-sidebar";
import { useIsMobile } from "@/components/hooks/use-mobile";
import { CreditsProvider } from "@/components/providers/credits-provider";

interface UsoLayoutProps {
  children: ReactNode;
}

export default function UsoLayout({ children }: UsoLayoutProps) {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const supabase = createClient();
  const isMobile = useIsMobile();

  useEffect(() => {
    const getUser = async () => {
      try {
        const { data, error } = await supabase.auth.getUser();
        if (error) {
          if (process.env.NODE_ENV === 'development') {
            console.error("Erro de autenticação (dev only):", error.code);
          }
          setUser(null);
          router.push("/sign-in");
        } else {
          setUser(data.user);
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error("Erro na verificação de auth (dev only)");
        }
        setUser(null);
        router.push("/sign-in");
      } finally {
        setLoading(false);
      }
    };

    getUser();

    // Set up auth state change listener
    const { data: authListener } = supabase.auth.onAuthStateChange(
      (event, session) => {
        if (event === "SIGNED_IN" && session) {
          setUser(session.user);
        } else if (event === "SIGNED_OUT") {
          setUser(null);
          router.push("/sign-in");
        }
      }
    );

    return () => {
      // Clean up the subscription
      authListener.subscription.unsubscribe();
    };
  }, [supabase.auth, router]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen w-full">
        <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
      </div>
    );
  }

  if (!user) {
    return null; // Será redirecionado pelo useEffect
  }

  return (
    <CreditsProvider userId={user.id}>
      <div className={`h-screen w-screen flex overflow-hidden ${isMobile ? 'mobile-container' : ''}`}>
        {/* Sidebar principal */}
        <MainSidebar userId={user.id} />

        {/* Área principal de conteúdo */}
        <div className="flex-1 overflow-auto">
          {children}
        </div>
      </div>
    </CreditsProvider>
  );
}
