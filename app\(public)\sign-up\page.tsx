import { Message } from "@/components/form-message";
import SignupFormClient from "@/components/auth/signup-form-client";
import { SignupFormStatic } from "@/components/auth/static-forms";
import LayoutAuth from "../layout-auth";
import { MobileSafe } from "@/components/ui/mobile-safe";

export default async function Signup(props: {
  searchParams: Promise<Message>;
}) {
  const searchParams = await props.searchParams;
  return (
    <LayoutAuth>
      <MobileSafe fallback={<SignupFormStatic />}>
        <SignupFormClient initialMessage={searchParams} />
      </MobileSafe>
    </LayoutAuth>
  );
}
