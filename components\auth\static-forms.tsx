import Link from "next/link";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

export function LoginFormStatic() {
  return (
    <div className="w-full flex flex-col">
      <h1 className="text-2xl font-medium text-center mb-2">Entrar</h1>
      <p className="text-sm text-foreground text-center mb-8">
        Não tem uma conta?{" "}
        <Link className="text-primary font-medium underline" href="/sign-up">
          Criar conta
        </Link>
      </p>
      <div className="flex flex-col gap-4">
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input name="email" placeholder="<EMAIL>" required />
        </div>
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <Label htmlFor="password"><PERSON>ha</Label>
            <Link
              className="text-xs text-foreground underline"
              href="/forgot-password"
            >
              Esqueceu a senha?
            </Link>
          </div>
          <Input
            type="password"
            name="password"
            placeholder="Sua senha"
            required
          />
        </div>
        <div className="pt-4">
          <Button className="w-full" disabled>
            Carregando...
          </Button>
        </div>
      </div>
    </div>
  );
}

export function SignupFormStatic() {
  return (
    <div className="w-full flex flex-col">
      <h1 className="text-2xl font-medium text-center mb-2">Criar conta</h1>
      <p className="text-sm text-foreground text-center mb-8">
        Já tem uma conta?{" "}
        <Link className="text-primary underline" href="/sign-in">
          Entrar
        </Link>
      </p>
      <div className="flex flex-col gap-4">
        <div className="space-y-2">
          <Label htmlFor="full_name">Nome completo</Label>
          <Input name="full_name" placeholder="Seu nome" required />
        </div>
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input name="email" placeholder="<EMAIL>" required />
        </div>
        <div className="space-y-2">
          <Label htmlFor="password">Senha</Label>
          <Input
            type="password"
            name="password"
            placeholder="Sua senha"
            required
          />
        </div>
        <div className="pt-4">
          <Button className="w-full" disabled>
            Carregando...
          </Button>
        </div>
      </div>
    </div>
  );
}

export function ForgotPasswordFormStatic() {
  return (
    <div className="w-full flex flex-col">
      <h1 className="text-2xl font-medium text-center mb-2">Redefinir senha</h1>
      <p className="text-sm text-foreground text-center mb-8">
        Já tem uma conta?{" "}
        <Link className="text-primary underline" href="/sign-in">
          Entrar
        </Link>
      </p>
      <div className="flex flex-col gap-4">
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input name="email" placeholder="<EMAIL>" required />
        </div>
        <div className="pt-4">
          <Button className="w-full" disabled>
            Carregando...
          </Button>
        </div>
      </div>
    </div>
  );
}

export function ResetPasswordFormStatic() {
  return (
    <div className="w-full flex flex-col">
      <h1 className="text-2xl font-medium text-center mb-2">Redefinir senha</h1>
      <p className="text-sm text-foreground text-center mb-8">
        Digite sua nova senha abaixo.
      </p>
      <div className="flex flex-col gap-4">
        <div className="space-y-2">
          <Label htmlFor="password">Nova senha</Label>
          <Input
            type="password"
            name="password"
            placeholder="Digite sua nova senha"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="confirmPassword">Confirmar senha</Label>
          <Input
            type="password"
            name="confirmPassword"
            placeholder="Confirme sua nova senha"
            required
          />
        </div>
        <div className="pt-4">
          <Button className="w-full" disabled>
            Carregando...
          </Button>
        </div>
      </div>
    </div>
  );
}
