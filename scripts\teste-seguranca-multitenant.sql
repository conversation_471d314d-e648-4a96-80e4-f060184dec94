-- =====================================================
-- TESTE DE SEGURANÇA MULTI-TENANT
-- Execute no Supabase SQL Editor
-- =====================================================

-- 1. CRIAR TENANTS DE TESTE
-- =====================================================
INSERT INTO app.tenants (subdomain, name, primary_color, secondary_color, settings) VALUES
('vasco', 'Vasco FC', '#000000', '#ffffff', '{"test": true}'),
('flamengo', 'Flamengo', '#dc2626', '#ffffff', '{"test": true}')
ON CONFLICT (subdomain) DO NOTHING;

-- 2. CRIAR USUÁRIOS DE TESTE EM TENANTS DIFERENTES
-- =====================================================
DO $$
DECLARE
    vasco_tenant_id UUID;
    flamengo_tenant_id UUID;
BEGIN
    -- Buscar IDs dos tenants
    SELECT id INTO vasco_tenant_id FROM app.tenants WHERE subdomain = 'vasco';
    SELECT id INTO flamengo_tenant_id FROM app.tenants WHERE subdomain = 'flamengo';
    
    -- Criar usuários de teste
    INSERT INTO app.users (user_id, full_name, email, tenant_id, credits) VALUES
    ('11111111-1111-1111-1111-111111111111', 'Usuário Vasco', '<EMAIL>', vasco_tenant_id, 100),
    ('*************-2222-2222-************', 'Usuário Flamengo', '<EMAIL>', flamengo_tenant_id, 100)
    ON CONFLICT (user_id) DO NOTHING;
    
    -- Criar dados de teste para cada tenant
    INSERT INTO app.agent_interaction_costs (
        user_id, session_id, invocation_id, agent_author, 
        prompt_token_count, candidates_token_count, thoughts_token_count,
        total_cost_usd, response_timestamp, tenant_id
    ) VALUES
    ('11111111-1111-1111-1111-111111111111', 'session-vasco-1', 'inv-vasco-1', 'test-agent',
     100, 200, 50, 0.001, NOW(), vasco_tenant_id),
    ('*************-2222-2222-************', 'session-flamengo-1', 'inv-flamengo-1', 'test-agent',
     150, 300, 75, 0.002, NOW(), flamengo_tenant_id)
    ON CONFLICT DO NOTHING;
END $$;

-- 3. TESTE DE ISOLAMENTO - TENANT VASCO
-- =====================================================
DO $$
DECLARE
    vasco_tenant_id UUID;
    user_count INTEGER;
    cost_count INTEGER;
BEGIN
    -- Buscar ID do tenant Vasco
    SELECT id INTO vasco_tenant_id FROM app.tenants WHERE subdomain = 'vasco';
    
    -- Definir contexto para tenant Vasco
    PERFORM app.set_current_tenant(vasco_tenant_id);
    
    -- Verificar isolamento de usuários
    SELECT COUNT(*) INTO user_count FROM app.users;
    RAISE NOTICE 'TESTE VASCO - Usuários visíveis: %', user_count;
    
    -- Verificar isolamento de custos
    SELECT COUNT(*) INTO cost_count FROM app.agent_interaction_costs;
    RAISE NOTICE 'TESTE VASCO - Custos visíveis: %', cost_count;
    
    -- Verificar dados específicos
    IF user_count = 1 AND cost_count = 1 THEN
        RAISE NOTICE '✅ TESTE VASCO PASSOU - Isolamento funcionando';
    ELSE
        RAISE NOTICE '❌ TESTE VASCO FALHOU - Vazamento de dados detectado';
    END IF;
END $$;

-- 4. TESTE DE ISOLAMENTO - TENANT FLAMENGO
-- =====================================================
DO $$
DECLARE
    flamengo_tenant_id UUID;
    user_count INTEGER;
    cost_count INTEGER;
BEGIN
    -- Buscar ID do tenant Flamengo
    SELECT id INTO flamengo_tenant_id FROM app.tenants WHERE subdomain = 'flamengo';
    
    -- Definir contexto para tenant Flamengo
    PERFORM app.set_current_tenant(flamengo_tenant_id);
    
    -- Verificar isolamento de usuários
    SELECT COUNT(*) INTO user_count FROM app.users;
    RAISE NOTICE 'TESTE FLAMENGO - Usuários visíveis: %', user_count;
    
    -- Verificar isolamento de custos
    SELECT COUNT(*) INTO cost_count FROM app.agent_interaction_costs;
    RAISE NOTICE 'TESTE FLAMENGO - Custos visíveis: %', cost_count;
    
    -- Verificar dados específicos
    IF user_count = 1 AND cost_count = 1 THEN
        RAISE NOTICE '✅ TESTE FLAMENGO PASSOU - Isolamento funcionando';
    ELSE
        RAISE NOTICE '❌ TESTE FLAMENGO FALHOU - Vazamento de dados detectado';
    END IF;
END $$;

-- 5. TESTE SEM CONTEXTO (DEVE VER TUDO OU NADA)
-- =====================================================
DO $$
DECLARE
    user_count INTEGER;
    cost_count INTEGER;
BEGIN
    -- Limpar contexto de tenant
    PERFORM set_config('app.current_tenant_id', '', false);
    
    -- Verificar o que é visível sem contexto
    SELECT COUNT(*) INTO user_count FROM app.users;
    SELECT COUNT(*) INTO cost_count FROM app.agent_interaction_costs;
    
    RAISE NOTICE 'TESTE SEM CONTEXTO - Usuários visíveis: %', user_count;
    RAISE NOTICE 'TESTE SEM CONTEXTO - Custos visíveis: %', cost_count;
    
    -- Com RLS ativo, sem contexto deve ver apenas dados sem tenant_id (NULL)
    IF user_count = 0 AND cost_count = 0 THEN
        RAISE NOTICE '✅ TESTE SEM CONTEXTO PASSOU - RLS bloqueando acesso';
    ELSE
        RAISE NOTICE '⚠️ TESTE SEM CONTEXTO - Verificar se é comportamento esperado';
    END IF;
END $$;

-- 6. VERIFICAR CONFIGURAÇÃO RLS
-- =====================================================
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'app' 
AND tablename IN ('users', 'agent_interaction_costs', 'tenants')
ORDER BY tablename;

-- 7. VERIFICAR POLÍTICAS RLS
-- =====================================================
SELECT 
    schemaname,
    tablename,
    policyname,
    cmd as command,
    permissive,
    qual as condition
FROM pg_policies 
WHERE schemaname = 'app'
ORDER BY tablename, policyname;

-- 8. VERIFICAR FUNÇÕES DE TENANT
-- =====================================================
SELECT 
    routine_name,
    routine_schema,
    routine_type
FROM information_schema.routines 
WHERE routine_schema = 'app' 
AND routine_name IN ('set_current_tenant', 'get_current_tenant_id')
ORDER BY routine_name;

-- 9. TESTE DE FUNÇÃO GET_CURRENT_TENANT_ID
-- =====================================================
DO $$
DECLARE
    vasco_tenant_id UUID;
    current_tenant UUID;
BEGIN
    -- Buscar ID do tenant Vasco
    SELECT id INTO vasco_tenant_id FROM app.tenants WHERE subdomain = 'vasco';
    
    -- Definir tenant
    PERFORM app.set_current_tenant(vasco_tenant_id);
    
    -- Verificar se função retorna o tenant correto
    SELECT app.get_current_tenant_id() INTO current_tenant;
    
    IF current_tenant = vasco_tenant_id THEN
        RAISE NOTICE '✅ FUNÇÃO GET_CURRENT_TENANT_ID FUNCIONANDO';
    ELSE
        RAISE NOTICE '❌ FUNÇÃO GET_CURRENT_TENANT_ID FALHOU';
    END IF;
END $$;

-- 10. LIMPEZA (OPCIONAL - DESCOMENTE PARA LIMPAR DADOS DE TESTE)
-- =====================================================
/*
DELETE FROM app.agent_interaction_costs WHERE session_id LIKE 'session-%test%';
DELETE FROM app.users WHERE email LIKE '%@test.com';
DELETE FROM app.tenants WHERE subdomain IN ('vasco', 'flamengo');
*/

-- =====================================================
-- RESULTADO ESPERADO:
-- - Todos os testes devem PASSAR (✅)
-- - RLS deve estar HABILITADO
-- - Políticas devem estar ATIVAS
-- - Funções devem estar no schema 'app'
-- =====================================================
