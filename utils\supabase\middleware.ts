import { createServerClient } from "@supabase/ssr";
import { type NextRequest, NextResponse } from "next/server";

// Função para extrair subdomínio
function extractSubdomain(request: NextRequest): string | null {
  const url = request.url;
  const host = request.headers.get('host') || '';
  const hostname = host.split(':')[0];

  // Desenvolvimento local
  if (url.includes('localhost') || url.includes('127.0.0.1')) {
    const fullUrlMatch = url.match(/http:\/\/([^.]+)\.localhost/);
    if (fullUrlMatch && fullUrlMatch[1]) {
      return fullUrlMatch[1];
    }
    if (hostname.includes('.localhost')) {
      return hostname.split('.')[0];
    }
    return null;
  }

  // Produção - domínio vascofa.shop
  const rootDomain = 'vascofa.shop';

  // URLs de preview do Vercel (tenant---branch.vercel.app)
  if (hostname.includes('---') && hostname.endsWith('.vercel.app')) {
    const parts = hostname.split('---');
    return parts.length > 0 ? parts[0] : null;
  }

  // Detecção regular de subdomínio
  const isSubdomain = hostname !== rootDomain &&
                     hostname !== `www.${rootDomain}` &&
                     hostname.endsWith(`.${rootDomain}`);

  return isSubdomain ? hostname.replace(`.${rootDomain}`, '') : null;
}

export const updateSession = async (request: NextRequest) => {
  try {
    const { pathname } = request.nextUrl;
    const subdomain = extractSubdomain(request);

    // Create an unmodified response
    let response = NextResponse.next({
      request: {
        headers: request.headers,
      },
    });

    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value }) =>
              request.cookies.set(name, value),
            );
            response = NextResponse.next({
              request,
            });
            cookiesToSet.forEach(({ name, value, options }) =>
              response.cookies.set(name, value, options),
            );
          },
        },
      },
    );

    // LÓGICA MULTI-TENANT
    if (subdomain) {
      // Verificar se o tenant existe e está ativo
      const { data: tenant, error: tenantError } = await supabase
        .from('tenants')
        .select('id, is_active, name')
        .eq('subdomain', subdomain)
        .eq('is_active', true)
        .single();

      if (tenantError || !tenant) {
        // Tenant não encontrado - redirecionar para página de erro
        return NextResponse.redirect(new URL('/tenant-not-found', request.url));
      }

      // Bloquear acesso a rotas administrativas em subdomínios
      if (pathname.startsWith('/admin')) {
        return NextResponse.redirect(new URL('/', request.url));
      }

      // Definir tenant no contexto para RLS
      await supabase.rpc('set_current_tenant', { tenant_uuid: tenant.id });

      // Adicionar headers com informações do tenant
      response.headers.set('x-tenant-id', tenant.id);
      response.headers.set('x-tenant-subdomain', subdomain);
      response.headers.set('x-tenant-name', tenant.name);
    }

    // LÓGICA DE AUTENTICAÇÃO EXISTENTE
    // This will refresh session if expired - required for Server Components
    const user = await supabase.auth.getUser();

    // Proteção de rotas de chat
    if (pathname.startsWith("/chat") && user.error) {
      return NextResponse.redirect(new URL("/sign-in", request.url));
    }

    // Redirecionar usuários autenticados da página inicial para o chat
    // APENAS no domínio principal (sem subdomínio)
    if (!subdomain && (pathname === "/" ||
         pathname.startsWith("/sign-in") ||
         pathname.startsWith("/sign-up") ||
         pathname.startsWith("/forgot-password")) && !user.error) {
      return NextResponse.redirect(new URL("/chat", request.url));
    }

    return response;
  } catch (e) {
    // If you are here, a Supabase client could not be created!
    // This is likely because you have not set up environment variables.
    // Check out http://localhost:3000 for Next Steps.
    return NextResponse.next({
      request: {
        headers: request.headers,
      },
    });
  }
};
