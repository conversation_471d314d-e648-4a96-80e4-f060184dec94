import { <PERSON><PERSON><PERSON>, <PERSON>ren<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "./ui/button";
import Link from "next/link";

export default function Hero() {
  return (
    <div className="relative overflow-hidden bg-gradient-to-br from-background via-background to-muted/20 h-screen flex items-center">
      {/* Main Content */}
      <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8 w-full">
        <div className="text-center space-y-6">

          {/* Badge */}
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 border border-primary/20 text-primary text-sm font-medium">
            <Brain className="w-4 h-4" />
            Inteligência Artificial para o Mercado Financeiro
          </div>

          {/* Main Heading */}
          <div className="space-y-2">
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight">
              <span className="block text-foreground">Transforme suas</span>
              <span className="block bg-gradient-to-r from-primary via-primary/80 to-primary/60 bg-clip-text text-transparent">
                Decisões Financeiras
              </span>
              <span className="block text-foreground">com IA Avançada</span>
            </h1>
          </div>

          {/* Subtitle */}
          <p className="max-w-2xl mx-auto text-base sm:text-lg text-muted-foreground leading-relaxed">
            A <strong className="text-foreground">PrimeAssetAI</strong> oferece tecnologia para que você consiga cruzar informações de todo mercado financeiro através de perguntas e esquecer das planilhas.
          </p>

          {/* Features */}
          <div className="flex flex-wrap justify-center gap-6 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <Brain className="w-4 h-4 text-green-500" />
              <span>Chat com IA</span>
            </div>
            <div className="flex items-center gap-2">
              <Database className="w-4 h-4 text-blue-500" />
              <span>Dados atualizados</span>
            </div>
            <div className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4 text-purple-500" />
              <span>Geração de gráficos</span>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center items-center pt-4">
            <Button asChild size="lg" className="group">
              <Link href="/chat" className="flex items-center gap-2">
                Começar Agora
                <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>

            <Button asChild variant="outline" size="lg">
              <Link href="/sign-up">
                Criar Conta Gratuita
              </Link>
            </Button>
          </div>

          {/* Trust Indicators */}
          <div className="pt-6 space-y-3">
            <p className="text-xs text-muted-foreground uppercase tracking-wider font-medium">
              Tecnologia Confiável
            </p>
            <div className="flex flex-wrap justify-center items-center gap-6 opacity-60">
              <div className="flex items-center gap-2 text-xs font-medium">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                Google Cloud
              </div>
              <div className="flex items-center gap-2 text-xs font-medium">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                Fontes de dados oficiais
              </div>
              <div className="flex items-center gap-2 text-xs font-medium">
                <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse" />
                Privacidade de dados
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Gradient */}
      <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-border to-transparent" />
    </div>
  );
}
