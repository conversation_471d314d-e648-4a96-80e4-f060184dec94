# Plano de Arquitetura Multi-Tenant para PrimeAI

## 1. Visão Geral da Arquitetura

### Estratégia Multi-Tenant
O PrimeAI será transformado em uma aplicação multi-tenant baseada em subdomínios, onde cada empresa/cliente terá seu próprio subdomínio (ex: `empresa1.primeai.com`, `empresa2.primeai.com`). A arquitetura utilizará:

- **Isolamento por Subdomínio**: Cada tenant acessa via subdomínio único
- **Banco de Dados Compartilhado**: Um único banco Supabase com isolamento por `tenant_id`
- **Frontend Personalizado**: Interface customizável por tenant
- **Deploy Unificado**: Uma única aplicação Next.js servindo todos os tenants

### Benefícios
- Escalabilidade horizontal
- Redução de custos operacionais
- Facilidade de manutenção
- Personalização por cliente
- Isolamento de dados garantido

## 2. Design do Banco de Dados

### Modificações no Schema Supabase

#### 2.1 Tabela de Tenants
```sql
-- Criar tabela de tenants
CREATE TABLE tenants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  subdomain VARCHAR(50) UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  logo_url TEXT,
  primary_color VARCHAR(7) DEFAULT '#000000',
  secondary_color VARCHAR(7) DEFAULT '#ffffff',
  custom_css TEXT,
  settings JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX idx_tenants_subdomain ON tenants(subdomain);
CREATE INDEX idx_tenants_active ON tenants(is_active);
```

#### 2.2 Modificação das Tabelas Existentes
Todas as tabelas existentes precisam adicionar `tenant_id`:

```sql
-- Adicionar tenant_id às tabelas existentes
ALTER TABLE agent_interaction_costs 
ADD COLUMN tenant_id UUID REFERENCES tenants(id);

-- Criar índices compostos para performance
CREATE INDEX idx_costs_tenant_user ON agent_interaction_costs(tenant_id, user_id);

-- Aplicar para todas as tabelas de dados financeiros
ALTER TABLE cotacoes_historicas_b3 ADD COLUMN tenant_id UUID REFERENCES tenants(id);
ALTER TABLE curvas_b3 ADD COLUMN tenant_id UUID REFERENCES tenants(id);
-- ... repetir para todas as tabelas
```

#### 2.3 Row Level Security (RLS)
```sql
-- Habilitar RLS nas tabelas
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_interaction_costs ENABLE ROW LEVEL SECURITY;

-- Políticas de segurança
CREATE POLICY tenant_isolation ON agent_interaction_costs
  FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

-- Função para definir tenant atual
CREATE OR REPLACE FUNCTION set_current_tenant(tenant_uuid UUID)
RETURNS void AS $$
BEGIN
  PERFORM set_config('app.current_tenant_id', tenant_uuid::text, true);
END;
$$ LANGUAGE plpgsql;
```

## 3. Implementação do Frontend

### 3.1 Middleware para Roteamento de Subdomínios

Criar `middleware.ts` na raiz do projeto:

```typescript
import { type NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/middleware';

function extractSubdomain(request: NextRequest): string | null {
  const url = request.url;
  const host = request.headers.get('host') || '';
  const hostname = host.split(':')[0];

  // Desenvolvimento local
  if (url.includes('localhost') || url.includes('127.0.0.1')) {
    const fullUrlMatch = url.match(/http:\/\/([^.]+)\.localhost/);
    if (fullUrlMatch && fullUrlMatch[1]) {
      return fullUrlMatch[1];
    }
    if (hostname.includes('.localhost')) {
      return hostname.split('.')[0];
    }
    return null;
  }

  // Produção
  const rootDomain = process.env.NEXT_PUBLIC_ROOT_DOMAIN || 'primeai.com';
  const rootDomainFormatted = rootDomain.split(':')[0];

  // URLs de preview do Vercel (tenant---branch.vercel.app)
  if (hostname.includes('---') && hostname.endsWith('.vercel.app')) {
    const parts = hostname.split('---');
    return parts.length > 0 ? parts[0] : null;
  }

  // Detecção regular de subdomínio
  const isSubdomain = hostname !== rootDomainFormatted && 
                     hostname !== `www.${rootDomainFormatted}` &&
                     hostname.endsWith(`.${rootDomainFormatted}`);
  
  return isSubdomain ? hostname.replace(`.${rootDomainFormatted}`, '') : null;
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const subdomain = extractSubdomain(request);

  if (subdomain) {
    // Verificar se o tenant existe
    const supabase = createClient(request);
    const { data: tenant } = await supabase
      .from('tenants')
      .select('id, is_active')
      .eq('subdomain', subdomain)
      .eq('is_active', true)
      .single();

    if (!tenant) {
      return NextResponse.redirect(new URL('/tenant-not-found', request.url));
    }

    // Bloquear acesso a rotas administrativas
    if (pathname.startsWith('/admin')) {
      return NextResponse.redirect(new URL('/', request.url));
    }

    // Reescrever para rota do tenant
    if (pathname === '/') {
      return NextResponse.rewrite(new URL(`/tenant/${subdomain}`, request.url));
    }

    // Adicionar header com tenant ID para uso na aplicação
    const response = NextResponse.next();
    response.headers.set('x-tenant-id', tenant.id);
    response.headers.set('x-tenant-subdomain', subdomain);
    return response;
  }

  // Domínio principal - permitir acesso normal
  return NextResponse.next();
}

export const config = {
  matcher: [
    '/((?!api|_next|[\\w-]+\\.\\w+).*)'
  ]
};
```

### 3.2 Context Provider para Tenant

Criar `lib/tenant-context.tsx`:

```typescript
'use client';

import { createContext, useContext, useEffect, useState } from 'react';

interface TenantData {
  id: string;
  subdomain: string;
  name: string;
  logoUrl?: string;
  primaryColor: string;
  secondaryColor: string;
  customCss?: string;
  settings: Record<string, any>;
}

interface TenantContextType {
  tenant: TenantData | null;
  isLoading: boolean;
}

const TenantContext = createContext<TenantContextType>({
  tenant: null,
  isLoading: true
});

export function TenantProvider({ children }: { children: React.ReactNode }) {
  const [tenant, setTenant] = useState<TenantData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function loadTenant() {
      try {
        const response = await fetch('/api/tenant/current');
        if (response.ok) {
          const tenantData = await response.json();
          setTenant(tenantData);
        }
      } catch (error) {
        console.error('Erro ao carregar dados do tenant:', error);
      } finally {
        setIsLoading(false);
      }
    }

    loadTenant();
  }, []);

  return (
    <TenantContext.Provider value={{ tenant, isLoading }}>
      {children}
    </TenantContext.Provider>
  );
}

export const useTenant = () => useContext(TenantContext);
```

### 3.3 API Route para Dados do Tenant

Criar `app/api/tenant/current/route.ts`:

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(request: NextRequest) {
  const subdomain = request.headers.get('x-tenant-subdomain');
  
  if (!subdomain) {
    return NextResponse.json({ error: 'Tenant não encontrado' }, { status: 404 });
  }

  const supabase = await createClient();
  
  const { data: tenant, error } = await supabase
    .from('tenants')
    .select('*')
    .eq('subdomain', subdomain)
    .eq('is_active', true)
    .single();

  if (error || !tenant) {
    return NextResponse.json({ error: 'Tenant não encontrado' }, { status: 404 });
  }

  return NextResponse.json({
    id: tenant.id,
    subdomain: tenant.subdomain,
    name: tenant.name,
    logoUrl: tenant.logo_url,
    primaryColor: tenant.primary_color,
    secondaryColor: tenant.secondary_color,
    customCss: tenant.custom_css,
    settings: tenant.settings
  });
}
```

## 4. Configuração do Vercel

### 4.1 Variáveis de Ambiente

Adicionar ao `.env.local` e configurar no Vercel:

```bash
# Domínio principal
NEXT_PUBLIC_ROOT_DOMAIN=primeai.com

# Supabase (existentes)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Multi-tenant
ENABLE_MULTI_TENANT=true
DEFAULT_TENANT_SUBDOMAIN=demo
```

### 4.2 Configuração de Domínio no Vercel

1. **Adicionar Domínio Principal**:
   - No dashboard do Vercel, adicionar `primeai.com`
   - Configurar DNS para apontar para Vercel

2. **Configurar Wildcard DNS**:
   - Adicionar registro DNS: `*.primeai.com` → Vercel
   - Isso permite subdomínios dinâmicos

3. **Configuração no next.config.ts**:

```typescript
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }
    return config;
  },
  
  // Configuração para multi-tenant
  async rewrites() {
    return [
      {
        source: '/tenant/:subdomain',
        destination: '/chat'
      }
    ];
  },
  
  // Headers para CORS e segurança
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN'
          }
        ]
      }
    ];
  }
};

export default nextConfig;
```

## 5. Considerações de Segurança

### 5.1 Isolamento de Dados
- **Row Level Security (RLS)** no Supabase
- **Validação de tenant_id** em todas as queries
- **Headers seguros** para prevenir ataques

### 5.2 Autenticação por Tenant
Modificar `utils/supabase/server.ts`:

```typescript
import { createServerClient } from "@supabase/ssr";
import { cookies, headers } from "next/headers";

export const createClient = async () => {
  const cookieStore = await cookies();
  const headersList = await headers();
  const tenantId = headersList.get('x-tenant-id');

  const client = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          } catch (error) {
            // Ignorar erros de Server Component
          }
        },
      },
    },
  );

  // Definir tenant atual para RLS
  if (tenantId) {
    await client.rpc('set_current_tenant', { tenant_uuid: tenantId });
  }

  return client;
};
```

### 5.3 Validação de Entrada
- **Sanitização** de dados de entrada
- **Validação** de subdomínios permitidos
- **Rate limiting** por tenant

## 6. Passos de Implementação

### Fase 1: Preparação do Banco (Semana 1)
1. ✅ Criar tabela `tenants`
2. ✅ Adicionar `tenant_id` às tabelas existentes
3. ✅ Implementar RLS
4. ✅ Migrar dados existentes para tenant padrão
5. ✅ Testar isolamento de dados

### Fase 2: Middleware e Roteamento (Semana 2)
1. ✅ Implementar middleware de subdomínios
2. ✅ Criar context provider para tenant
3. ✅ Desenvolver API routes para tenant
4. ✅ Testar roteamento local

### Fase 3: Interface Multi-Tenant (Semana 3)
1. ✅ Adaptar componentes para usar context
2. ✅ Implementar personalização visual
3. ✅ Criar painel administrativo
4. ✅ Desenvolver sistema de onboarding

### Fase 4: Deploy e Configuração (Semana 4)
1. ✅ Configurar domínios no Vercel
2. ✅ Implementar variáveis de ambiente
3. ✅ Testar em produção
4. ✅ Documentar processo de criação de tenants

## 7. Estratégia de Testes

### 7.1 Testes Unitários
```typescript
// __tests__/middleware.test.ts
import { extractSubdomain } from '../middleware';

describe('Middleware', () => {
  test('deve extrair subdomínio corretamente', () => {
    const request = new Request('http://empresa1.localhost:3000');
    const subdomain = extractSubdomain(request);
    expect(subdomain).toBe('empresa1');
  });
});
```

### 7.2 Testes de Integração
- Testar isolamento de dados entre tenants
- Verificar autenticação por tenant
- Validar personalização visual

### 7.3 Testes E2E
- Fluxo completo de criação de tenant
- Navegação entre subdomínios
- Funcionalidades específicas por tenant

## 8. Processo de Deploy

### 8.1 Preparação
1. **Backup do banco** atual
2. **Configurar variáveis** de ambiente
3. **Testar migrations** em staging

### 8.2 Deploy
1. **Deploy da aplicação** no Vercel
2. **Executar migrations** do banco
3. **Configurar DNS** wildcard
4. **Testar subdomínios** em produção

### 8.3 Pós-Deploy
1. **Monitorar logs** de erro
2. **Verificar performance**
3. **Documentar** processo para equipe
4. **Treinar usuários** no novo sistema

## 9. Personalização Visual por Tenant

### 9.1 Sistema de Temas Dinâmicos

Criar `components/tenant-theme-provider.tsx`:

```typescript
'use client';

import { useTenant } from '@/lib/tenant-context';
import { useEffect } from 'react';

export function TenantThemeProvider({ children }: { children: React.ReactNode }) {
  const { tenant } = useTenant();

  useEffect(() => {
    if (tenant) {
      // Aplicar cores personalizadas via CSS variables
      document.documentElement.style.setProperty('--primary-color', tenant.primaryColor);
      document.documentElement.style.setProperty('--secondary-color', tenant.secondaryColor);

      // Aplicar CSS customizado
      if (tenant.customCss) {
        const styleElement = document.createElement('style');
        styleElement.textContent = tenant.customCss;
        styleElement.id = 'tenant-custom-styles';

        // Remover estilos anteriores
        const existingStyles = document.getElementById('tenant-custom-styles');
        if (existingStyles) {
          existingStyles.remove();
        }

        document.head.appendChild(styleElement);
      }
    }
  }, [tenant]);

  return <>{children}</>;
}
```

### 9.2 Componente de Logo Dinâmico

Criar `components/tenant-logo.tsx`:

```typescript
import { useTenant } from '@/lib/tenant-context';
import Image from 'next/image';

export function TenantLogo({ className }: { className?: string }) {
  const { tenant } = useTenant();

  if (!tenant) {
    return <div className={`${className} animate-pulse bg-gray-200 rounded`} />;
  }

  return (
    <div className={className}>
      {tenant.logoUrl ? (
        <Image
          src={tenant.logoUrl}
          alt={`${tenant.name} Logo`}
          width={120}
          height={40}
          className="object-contain"
        />
      ) : (
        <h1 className="text-xl font-bold" style={{ color: tenant.primaryColor }}>
          {tenant.name}
        </h1>
      )}
    </div>
  );
}
```

## 10. Painel Administrativo

### 10.1 Interface de Gerenciamento de Tenants

Criar `app/admin/tenants/page.tsx`:

```typescript
'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface Tenant {
  id: string;
  subdomain: string;
  name: string;
  isActive: boolean;
  createdAt: string;
}

export default function TenantsPage() {
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [loading, setLoading] = useState(true);
  const [newTenant, setNewTenant] = useState({ subdomain: '', name: '' });

  useEffect(() => {
    loadTenants();
  }, []);

  const loadTenants = async () => {
    try {
      const response = await fetch('/api/admin/tenants');
      const data = await response.json();
      setTenants(data);
    } catch (error) {
      console.error('Erro ao carregar tenants:', error);
    } finally {
      setLoading(false);
    }
  };

  const createTenant = async () => {
    try {
      const response = await fetch('/api/admin/tenants', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newTenant)
      });

      if (response.ok) {
        setNewTenant({ subdomain: '', name: '' });
        loadTenants();
      }
    } catch (error) {
      console.error('Erro ao criar tenant:', error);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Gerenciamento de Tenants</h1>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Criar Novo Tenant</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Input
            placeholder="Subdomínio (ex: empresa1)"
            value={newTenant.subdomain}
            onChange={(e) => setNewTenant({ ...newTenant, subdomain: e.target.value })}
          />
          <Input
            placeholder="Nome da Empresa"
            value={newTenant.name}
            onChange={(e) => setNewTenant({ ...newTenant, name: e.target.value })}
          />
          <Button onClick={createTenant}>Criar Tenant</Button>
        </CardContent>
      </Card>

      <div className="grid gap-4">
        {tenants.map((tenant) => (
          <Card key={tenant.id}>
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="font-semibold">{tenant.name}</h3>
                  <p className="text-sm text-gray-600">{tenant.subdomain}.primeai.com</p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 rounded text-xs ${
                    tenant.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {tenant.isActive ? 'Ativo' : 'Inativo'}
                  </span>
                  <Button variant="outline" size="sm">Editar</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
```

### 10.2 API para Gerenciamento de Tenants

Criar `app/api/admin/tenants/route.ts`:

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET() {
  const supabase = await createClient();

  const { data: tenants, error } = await supabase
    .from('tenants')
    .select('id, subdomain, name, is_active, created_at')
    .order('created_at', { ascending: false });

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  return NextResponse.json(tenants);
}

export async function POST(request: NextRequest) {
  const { subdomain, name } = await request.json();

  // Validações
  if (!subdomain || !name) {
    return NextResponse.json({ error: 'Subdomínio e nome são obrigatórios' }, { status: 400 });
  }

  if (!/^[a-z0-9-]+$/.test(subdomain)) {
    return NextResponse.json({
      error: 'Subdomínio deve conter apenas letras minúsculas, números e hífens'
    }, { status: 400 });
  }

  const supabase = await createClient();

  const { data: tenant, error } = await supabase
    .from('tenants')
    .insert({
      subdomain,
      name,
      primary_color: '#000000',
      secondary_color: '#ffffff',
      settings: {}
    })
    .select()
    .single();

  if (error) {
    if (error.code === '23505') { // Unique constraint violation
      return NextResponse.json({ error: 'Subdomínio já existe' }, { status: 409 });
    }
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  return NextResponse.json(tenant, { status: 201 });
}
```

## 11. Monitoramento e Performance

### 11.1 Métricas por Tenant

Criar `lib/analytics.ts`:

```typescript
export interface TenantMetrics {
  tenantId: string;
  activeUsers: number;
  totalSessions: number;
  avgResponseTime: number;
  errorRate: number;
}

export async function getTenantMetrics(tenantId: string): Promise<TenantMetrics> {
  const supabase = await createClient();

  // Buscar métricas dos últimos 30 dias
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const { data: sessions } = await supabase
    .from('agent_interaction_costs')
    .select('user_id, response_timestamp, total_cost_usd')
    .eq('tenant_id', tenantId)
    .gte('response_timestamp', thirtyDaysAgo.toISOString());

  const activeUsers = new Set(sessions?.map(s => s.user_id)).size;
  const totalSessions = sessions?.length || 0;

  return {
    tenantId,
    activeUsers,
    totalSessions,
    avgResponseTime: 0, // Calcular baseado nos dados
    errorRate: 0 // Calcular baseado nos logs
  };
}
```

### 11.2 Cache por Tenant

Implementar cache Redis por tenant:

```typescript
// lib/cache.ts
import { Redis } from '@upstash/redis';

const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

export class TenantCache {
  private getTenantKey(tenantId: string, key: string): string {
    return `tenant:${tenantId}:${key}`;
  }

  async get<T>(tenantId: string, key: string): Promise<T | null> {
    const result = await redis.get(this.getTenantKey(tenantId, key));
    return result as T;
  }

  async set(tenantId: string, key: string, value: any, ttl: number = 3600): Promise<void> {
    await redis.setex(this.getTenantKey(tenantId, key), ttl, JSON.stringify(value));
  }

  async del(tenantId: string, key: string): Promise<void> {
    await redis.del(this.getTenantKey(tenantId, key));
  }
}

export const tenantCache = new TenantCache();
```

## 12. Migração de Dados Existentes

### 12.1 Script de Migração

Criar `scripts/migrate-to-multitenant.ts`:

```typescript
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

async function migrateExistingData() {
  console.log('Iniciando migração para multi-tenant...');

  // 1. Criar tenant padrão
  const { data: defaultTenant, error: tenantError } = await supabase
    .from('tenants')
    .insert({
      subdomain: 'demo',
      name: 'Demo Company',
      primary_color: '#000000',
      secondary_color: '#ffffff'
    })
    .select()
    .single();

  if (tenantError) {
    console.error('Erro ao criar tenant padrão:', tenantError);
    return;
  }

  console.log('Tenant padrão criado:', defaultTenant.id);

  // 2. Atualizar dados existentes
  const tables = [
    'agent_interaction_costs',
    'cotacoes_historicas_b3',
    'curvas_b3'
    // Adicionar todas as tabelas que precisam de tenant_id
  ];

  for (const table of tables) {
    console.log(`Migrando tabela: ${table}`);

    const { error } = await supabase
      .from(table)
      .update({ tenant_id: defaultTenant.id })
      .is('tenant_id', null);

    if (error) {
      console.error(`Erro ao migrar ${table}:`, error);
    } else {
      console.log(`✅ ${table} migrada com sucesso`);
    }
  }

  console.log('Migração concluída!');
}

// Executar migração
migrateExistingData().catch(console.error);
```

---

## Próximos Passos

1. **Revisar** este plano com a equipe técnica
2. **Estimar** tempo de desenvolvimento (4-6 semanas)
3. **Configurar** ambiente de desenvolvimento com subdomínios locais
4. **Preparar** banco de dados de teste
5. **Iniciar** implementação seguindo as fases definidas

## Considerações Finais

Este plano abrangente transforma o PrimeAI em uma solução multi-tenant robusta, mantendo:

- **Segurança**: Isolamento completo de dados por tenant
- **Performance**: Cache otimizado e queries eficientes
- **Escalabilidade**: Arquitetura preparada para crescimento
- **Flexibilidade**: Personalização visual e funcional por cliente
- **Manutenibilidade**: Código organizado e bem documentado

A implementação seguirá as melhores práticas do Next.js 15 e Supabase, garantindo uma solução moderna e confiável para o ambiente de produção.
