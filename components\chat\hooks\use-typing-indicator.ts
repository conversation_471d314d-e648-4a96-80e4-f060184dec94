"use client";

import { useState, useCallback, useRef } from "react";
import { AppConfig } from "@/lib/config/app-config";

interface UseTypingIndicatorReturn {
  showTypingIndicator: boolean;
  startTyping: () => void;
  stopTyping: () => void;
  resetTyping: () => void;
  isTypingExpired: boolean;
}

/**
 * Hook personalizado para gerenciar o estado do indicador de digitação
 * com suporte a contagem regressiva e timeout automático
 */
export function useTypingIndicator(): UseTypingIndicatorReturn {
  const [showTypingIndicator, setShowTypingIndicator] = useState(false);
  const [isTypingExpired, setIsTypingExpired] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const startTyping = useCallback(() => {
    setShowTypingIndicator(true);
    setIsTypingExpired(false);

    // Limpar timeout anterior se existir
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Se a contagem regressiva estiver habilitada, configurar timeout automático
    if (AppConfig.typingIndicator.enableCountdown) {
      const timeoutDuration = AppConfig.typingIndicator.duration * 1000; // Converter para ms
      
      timeoutRef.current = setTimeout(() => {
        setIsTypingExpired(true);
        // Não parar automaticamente, deixar o componente pai decidir
      }, timeoutDuration);
    }
  }, []);

  const stopTyping = useCallback(() => {
    setShowTypingIndicator(false);
    setIsTypingExpired(false);
    
    // Limpar timeout se existir
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  const resetTyping = useCallback(() => {
    setIsTypingExpired(false);
    
    // Se ainda está mostrando o indicador, reiniciar o timer
    if (showTypingIndicator) {
      startTyping();
    }
  }, [showTypingIndicator, startTyping]);

  return {
    showTypingIndicator,
    startTyping,
    stopTyping,
    resetTyping,
    isTypingExpired
  };
}
