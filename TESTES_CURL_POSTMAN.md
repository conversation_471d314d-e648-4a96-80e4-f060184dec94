# 🧪 TESTES cURL PARA DIAGNÓSTICO RLS

## 📋 CONFIGURAÇÃO INICIAL

### **Variáveis para Postman:**
```
SUPABASE_URL: https://opawvlqvsbltimqlzbdo.supabase.co
ANON_KEY: [sua_anon_key]
SERVICE_KEY: [sua_service_role_key]
USER_ID: 38cc3c24-7d83-4194-bcc9-68a501ceff2d
TENANT_ID: 1c11dd1a-97a8-45fd-a295-f56963f50f9a
```

## 🔍 TESTE 1: VERIFICAR TABELA USERS (SEM CONTEXTO)

### **cURL:**
```bash
curl -X GET "https://opawvlqvsbltimqlzbdo.supabase.co/rest/v1/users" \
  -H "apikey: [ANON_KEY]" \
  -H "Authorization: Bearer [ANON_KEY]" \
  -H "Accept-Profile: app" \
  -H "Content-Type: application/json"
```

### **Postman:**
- **Method**: GET
- **URL**: `{{SUPABASE_URL}}/rest/v1/users`
- **Headers**:
  - `apikey`: `{{ANON_KEY}}`
  - `Authorization`: `Bearer {{ANON_KEY}}`
  - `Accept-Profile`: `app`
  - `Content-Type`: `application/json`

**Resultado Esperado**: Lista vazia ou erro (RLS bloqueando)

## 🔍 TESTE 2: DEFINIR CONTEXTO DE TENANT

### **cURL:**
```bash
curl -X POST "https://opawvlqvsbltimqlzbdo.supabase.co/rest/v1/rpc/set_current_tenant" \
  -H "apikey: [ANON_KEY]" \
  -H "Authorization: Bearer [ANON_KEY]" \
  -H "Accept-Profile: app" \
  -H "Content-Type: application/json" \
  -d '{"tenant_uuid": "1c11dd1a-97a8-45fd-a295-f56963f50f9a"}'
```

### **Postman:**
- **Method**: POST
- **URL**: `{{SUPABASE_URL}}/rest/v1/rpc/set_current_tenant`
- **Headers**:
  - `apikey`: `{{ANON_KEY}}`
  - `Authorization`: `Bearer {{ANON_KEY}}`
  - `Accept-Profile`: `app`
  - `Content-Type`: `application/json`
- **Body** (raw JSON):
```json
{
  "tenant_uuid": "1c11dd1a-97a8-45fd-a295-f56963f50f9a"
}
```

**Resultado Esperado**: Sucesso (200) ou erro se função não existir

## 🔍 TESTE 3: VERIFICAR CONTEXTO ATUAL

### **cURL:**
```bash
curl -X POST "https://opawvlqvsbltimqlzbdo.supabase.co/rest/v1/rpc/get_current_tenant_id" \
  -H "apikey: [ANON_KEY]" \
  -H "Authorization: Bearer [ANON_KEY]" \
  -H "Accept-Profile: app" \
  -H "Content-Type: application/json" \
  -d '{}'
```

### **Postman:**
- **Method**: POST
- **URL**: `{{SUPABASE_URL}}/rest/v1/rpc/get_current_tenant_id`
- **Headers**:
  - `apikey`: `{{ANON_KEY}}`
  - `Authorization`: `Bearer {{ANON_KEY}}`
  - `Accept-Profile`: `app`
  - `Content-Type`: `application/json`
- **Body** (raw JSON):
```json
{}
```

**Resultado Esperado**: UUID do tenant ou null

## 🔍 TESTE 4: BUSCAR USUÁRIO COM CONTEXTO

### **cURL:**
```bash
curl -X GET "https://opawvlqvsbltimqlzbdo.supabase.co/rest/v1/users?select=tenant_id,full_name,email&user_id=eq.38cc3c24-7d83-4194-bcc9-68a501ceff2d" \
  -H "apikey: [ANON_KEY]" \
  -H "Authorization: Bearer [ANON_KEY]" \
  -H "Accept-Profile: app" \
  -H "Content-Type: application/json"
```

### **Postman:**
- **Method**: GET
- **URL**: `{{SUPABASE_URL}}/rest/v1/users?select=tenant_id,full_name,email&user_id=eq.{{USER_ID}}`
- **Headers**:
  - `apikey`: `{{ANON_KEY}}`
  - `Authorization`: `Bearer {{ANON_KEY}}`
  - `Accept-Profile`: `app`
  - `Content-Type`: `application/json`

**Resultado Esperado**: Dados do usuário ou erro 22P02

## 🔍 TESTE 5: USAR SERVICE ROLE (BYPASS RLS)

### **cURL:**
```bash
curl -X GET "https://opawvlqvsbltimqlzbdo.supabase.co/rest/v1/users?select=tenant_id,full_name,email&user_id=eq.38cc3c24-7d83-4194-bcc9-68a501ceff2d" \
  -H "apikey: [SERVICE_KEY]" \
  -H "Authorization: Bearer [SERVICE_KEY]" \
  -H "Accept-Profile: app" \
  -H "Content-Type: application/json"
```

### **Postman:**
- **Method**: GET
- **URL**: `{{SUPABASE_URL}}/rest/v1/users?select=tenant_id,full_name,email&user_id=eq.{{USER_ID}}`
- **Headers**:
  - `apikey`: `{{SERVICE_KEY}}`
  - `Authorization`: `Bearer {{SERVICE_KEY}}`
  - `Accept-Profile`: `app`
  - `Content-Type`: `application/json`

**Resultado Esperado**: Dados do usuário (deve funcionar)

## 🔍 TESTE 6: VERIFICAR POLÍTICAS RLS

### **cURL:**
```bash
curl -X POST "https://opawvlqvsbltimqlzbdo.supabase.co/rest/v1/rpc/sql" \
  -H "apikey: [SERVICE_KEY]" \
  -H "Authorization: Bearer [SERVICE_KEY]" \
  -H "Content-Type: application/json" \
  -d '{"query": "SELECT * FROM pg_policies WHERE schemaname = '\''app'\'' AND tablename = '\''users'\''"}'
```

### **Postman:**
- **Method**: POST
- **URL**: `{{SUPABASE_URL}}/rest/v1/rpc/sql`
- **Headers**:
  - `apikey`: `{{SERVICE_KEY}}`
  - `Authorization`: `Bearer {{SERVICE_KEY}}`
  - `Content-Type`: `application/json`
- **Body** (raw JSON):
```json
{
  "query": "SELECT * FROM pg_policies WHERE schemaname = 'app' AND tablename = 'users'"
}
```

## 🔍 TESTE 7: SEQUÊNCIA COMPLETA (POSTMAN COLLECTION)

### **1. Definir Contexto**
POST `/rpc/set_current_tenant` com tenant_uuid

### **2. Verificar Contexto**
POST `/rpc/get_current_tenant_id` (deve retornar UUID)

### **3. Buscar Usuário**
GET `/users?select=...&user_id=eq.{{USER_ID}}`

### **4. Verificar Tenants**
GET `/tenants?select=id,subdomain,name&subdomain=eq.vasco`

## 📊 DIAGNÓSTICO ESPERADO

### **Se RLS está funcionando:**
- Teste 1: Lista vazia (RLS bloqueando)
- Teste 2: Sucesso (contexto definido)
- Teste 3: Retorna UUID do tenant
- Teste 4: Retorna dados do usuário
- Teste 5: Sempre funciona (service role)

### **Se RLS tem problema:**
- Teste 2: Erro (função não existe)
- Teste 3: Retorna null ou string vazia
- Teste 4: Erro 22P02 (UUID vazio)

## 🛠️ POSSÍVEIS PROBLEMAS

### **1. Função não existe no schema app**
```sql
-- Verificar se existe
SELECT * FROM information_schema.routines 
WHERE routine_schema = 'app' 
AND routine_name IN ('set_current_tenant', 'get_current_tenant_id');
```

### **2. Política RLS incorreta**
```sql
-- Verificar política
SELECT qual FROM pg_policies 
WHERE schemaname = 'app' AND tablename = 'users';
```

### **3. Contexto não persiste entre chamadas**
- Cada chamada HTTP é independente
- Contexto precisa ser redefinido a cada request

## 🎯 PRÓXIMOS PASSOS

1. **Execute os testes no Postman**
2. **Identifique onde falha**
3. **Compare resultados esperados**
4. **Reporte os resultados**

---

**Execute os testes e me mostre os resultados! Vamos identificar exatamente onde o RLS está falhando. 🔍**
