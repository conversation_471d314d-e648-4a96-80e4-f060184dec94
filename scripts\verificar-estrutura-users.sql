-- =====================================================
-- VERIFICAÇÃO COMPLETA DA TABELA USERS
-- Execute no Supabase SQL Editor
-- =====================================================

-- 1. VERIFICAR SE A TABELA EXISTE
-- =====================================================
SELECT 
    schemaname,
    tablename,
    tableowner,
    hasindexes,
    hasrules,
    hastriggers,
    rowsecurity
FROM pg_tables 
WHERE schemaname = 'app' 
AND tablename = 'users';

-- 2. VERIFICAR ESTRUTURA DA TABELA
-- =====================================================
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_schema = 'app' 
AND table_name = 'users'
ORDER BY ordinal_position;

-- 3. VERIFICAR DADOS EXISTENTES
-- =====================================================
-- Contar total de registros (sem RLS)
SELECT COUNT(*) as total_users FROM app.users;

-- Ver todos os dados (sem RLS)
SELECT 
    user_id,
    full_name,
    email,
    tenant_id,
    credits,
    created_at
FROM app.users
ORDER BY created_at;

-- 4. VERIFICAR POLÍTICAS RLS
-- =====================================================
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual as condition
FROM pg_policies 
WHERE schemaname = 'app'
AND tablename = 'users'
ORDER BY policyname;

-- 5. VERIFICAR PERMISSÕES DA TABELA
-- =====================================================
SELECT 
    grantee,
    privilege_type,
    is_grantable
FROM information_schema.table_privileges 
WHERE table_schema = 'app' 
AND table_name = 'users'
ORDER BY grantee, privilege_type;

-- 6. VERIFICAR ROLE ANON
-- =====================================================
-- Verificar se role anon tem permissões
SELECT 
    'anon permissions' as check_type,
    grantee,
    privilege_type
FROM information_schema.table_privileges 
WHERE table_schema = 'app' 
AND table_name = 'users'
AND grantee = 'anon';

-- 7. TESTAR ACESSO COM DIFERENTES CONTEXTOS
-- =====================================================

-- Teste 1: Sem contexto de tenant
SELECT set_config('app.current_tenant_id', '', false);
SELECT 'SEM CONTEXTO' as teste, COUNT(*) as count FROM app.users;

-- Teste 2: Com contexto do tenant Vasco
SELECT app.set_current_tenant('1c11dd1a-97a8-45fd-a295-f56963f50f9a');
SELECT 'COM CONTEXTO VASCO' as teste, COUNT(*) as count FROM app.users;

-- Teste 3: Buscar usuário específico com contexto
SELECT 
    'BUSCA ESPECÍFICA' as teste,
    user_id,
    full_name,
    tenant_id
FROM app.users 
WHERE user_id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d';

-- 8. VERIFICAR SE USUÁRIO EXISTE NO AUTH.USERS
-- =====================================================
SELECT 
    'AUTH.USERS' as tabela,
    id,
    email,
    created_at,
    raw_user_meta_data
FROM auth.users 
WHERE id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d';

-- 9. VERIFICAR MISMATCH ENTRE AUTH.USERS E APP.USERS
-- =====================================================
SELECT 
    'MISMATCH CHECK' as check_type,
    au.id as auth_user_id,
    au.email as auth_email,
    u.user_id as app_user_id,
    u.email as app_email,
    u.tenant_id
FROM auth.users au
LEFT JOIN app.users u ON au.id = u.user_id
WHERE au.id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d';

-- 10. VERIFICAR TRIGGER HANDLE_NEW_USER
-- =====================================================
SELECT 
    trigger_name,
    event_manipulation,
    action_timing,
    action_statement
FROM information_schema.triggers 
WHERE event_object_schema = 'auth'
AND event_object_table = 'users'
AND trigger_name LIKE '%new_user%';

-- 11. CRIAR USUÁRIO DE TESTE SE NÃO EXISTIR
-- =====================================================
DO $$
BEGIN
    -- Verificar se usuário existe em app.users
    IF NOT EXISTS (
        SELECT 1 FROM app.users 
        WHERE user_id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d'
    ) THEN
        -- Inserir usuário de teste
        INSERT INTO app.users (
            user_id, 
            full_name, 
            email, 
            tenant_id, 
            credits
        ) VALUES (
            '38cc3c24-7d83-4194-bcc9-68a501ceff2d',
            'Usuário Teste',
            '<EMAIL>',
            '1c11dd1a-97a8-45fd-a295-f56963f50f9a',
            100
        );
        RAISE NOTICE 'Usuário de teste criado em app.users';
    ELSE
        RAISE NOTICE 'Usuário já existe em app.users';
    END IF;
END $$;

-- 12. VERIFICAR RESULTADO FINAL
-- =====================================================
-- Com contexto do tenant Vasco
SELECT app.set_current_tenant('1c11dd1a-97a8-45fd-a295-f56963f50f9a');

SELECT 
    'VERIFICAÇÃO FINAL' as teste,
    user_id,
    full_name,
    email,
    tenant_id,
    credits
FROM app.users 
WHERE user_id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d';

-- 13. TESTAR QUERY EXATA QUE ESTÁ FALHANDO
-- =====================================================
-- Simular a query que está dando erro 400
SELECT 
    'QUERY SIMULADA' as teste,
    tenant_id
FROM app.users 
WHERE user_id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d';

-- =====================================================
-- RESULTADO ESPERADO:
-- - Tabela users deve existir no schema app
-- - Usuário deve existir com tenant_id correto
-- - RLS deve estar habilitado
-- - Políticas devem permitir acesso com contexto
-- - Query deve retornar dados quando contexto definido
-- =====================================================
