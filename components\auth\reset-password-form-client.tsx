"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { resetPasswordClient } from "@/app/client-actions";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { FormMessage, Message } from "@/components/form-message";

export default function ResetPasswordFormClient({ initialMessage }: { initialMessage?: Message }) {
  const router = useRouter();
  const [message, setMessage] = useState<Message | null>(initialMessage || null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsSubmitting(true);

    try {
      const formData = new FormData(event.currentTarget);
      const result = await resetPasswordClient(formData);

      if (result.success) {
        const successMessage = result.message || "Senha atualizada com sucesso!";

        // Toast de sucesso
        toast.success("Senha atualizada!", {
          description: "Redirecionando para o login...",
          duration: 3000,
        });

        setMessage({
          success: successMessage
        });

        // Redirecionar para a página de login após 2 segundos
        setTimeout(() => {
          router.push("/sign-in");
        }, 2000);
      }
    } catch (error: any) {
      const errorMessage = error.message || "Falha ao redefinir senha";

      // Definir mensagem de erro mais específica
      let userFriendlyMessage = errorMessage;
      if (errorMessage.includes("Password should be at least")) {
        userFriendlyMessage = "A senha deve ter pelo menos 6 caracteres";
      } else if (errorMessage.includes("Passwords do not match")) {
        userFriendlyMessage = "As senhas não coincidem";
      } else if (errorMessage.includes("Session expired")) {
        userFriendlyMessage = "Sessão expirada. Solicite um novo link de redefinição";
      }

      // Toast de erro
      toast.error("Erro ao redefinir senha", {
        description: userFriendlyMessage,
        duration: 4000,
        action: {
          label: "Tentar novamente",
          onClick: () => {
            setMessage(null);
          }
        }
      });

      setMessage({
        error: userFriendlyMessage
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-card rounded-lg shadow-sm p-8">
        <h1 className="text-2xl font-semibold mb-6">Nova senha</h1>
        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="password">Nova senha</Label>
              <Input
                type="password"
                name="password"
                placeholder="Nova senha"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirmar senha</Label>
              <Input
                type="password"
                name="confirmPassword"
                placeholder="Confirmar senha"
                required
              />
            </div>
            <div className="pt-4">
              <SubmitButton
                className="w-full"
                disabled={isSubmitting}
                pendingText="Atualizando senha..."
              >
                Atualizar senha
              </SubmitButton>
            </div>
            {message && <FormMessage message={message} />}
          </div>
        </form>
      </div>
    </div>
  );
}
