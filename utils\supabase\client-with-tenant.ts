import { createBrowserClient } from "@supabase/ssr";

// Cache para tenant_id para evitar múltiplas consultas
let cachedTenantId: string | null = null;
let tenantPromise: Promise<string | null> | null = null;

// Função para extrair subdomínio no cliente
function extractSubdomainClient(): string | null {
  if (typeof window === 'undefined') return null;
  
  const hostname = window.location.hostname;
  
  // Desenvolvimento - localhost
  if (hostname.includes('.localhost')) {
    return hostname.split('.')[0];
  }
  
  // Produção - vascofa.shop
  const rootDomain = 'vascofa.shop';
  const isSubdomain = hostname !== rootDomain &&
                     hostname !== `www.${rootDomain}` &&
                     hostname.endsWith(`.${rootDomain}`);
  
  return isSubdomain ? hostname.replace(`.${rootDomain}`, '') : null;
}

// Função para buscar e cachear tenant_id
async function getTenantId(): Promise<string | null> {
  // Se já temos o tenant_id cacheado, retornar
  if (cachedTenantId) {
    return cachedTenantId;
  }

  // Se já há uma promise em andamento, aguardar ela
  if (tenantPromise) {
    return tenantPromise;
  }

  const subdomain = extractSubdomainClient();
  if (!subdomain) {
    return null;
  }

  // Criar promise para buscar tenant
  tenantPromise = (async () => {
    try {
      const client = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      );

      const { data: tenant, error } = await client
        .schema('app')
        .from('tenants')
        .select('id')
        .eq('subdomain', subdomain)
        .eq('is_active', true)
        .single();

      if (error || !tenant) {
        console.error(`[CLIENT] Tenant não encontrado para ${subdomain}:`, error);
        return null;
      }

      cachedTenantId = tenant.id;
      console.log(`[CLIENT] Tenant encontrado: ${tenant.id} (${subdomain})`);
      return tenant.id;
    } catch (error) {
      console.error(`[CLIENT] Erro ao buscar tenant:`, error);
      return null;
    } finally {
      tenantPromise = null;
    }
  })();

  return tenantPromise;
}

// Cliente Supabase com contexto de tenant automático
export const createClientWithTenant = async () => {
  const client = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  );

  // Buscar e definir contexto de tenant
  const tenantId = await getTenantId();
  
  if (tenantId) {
    try {
      await client.schema('app').rpc('set_current_tenant', { tenant_uuid: tenantId });
      console.log(`[CLIENT] Contexto de tenant definido: ${tenantId}`);
    } catch (rpcError) {
      console.error(`[CLIENT] Erro ao definir contexto de tenant:`, rpcError);
    }
  } else {
    console.warn(`[CLIENT] Nenhum tenant encontrado - operações podem falhar`);
  }

  return client;
};

// Cliente padrão (sem contexto automático)
export const createClient = () => {
  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  );
};

// Função para limpar cache (útil para testes)
export const clearTenantCache = () => {
  cachedTenantId = null;
  tenantPromise = null;
};
