/**
 * Logger seguro para operações do Supabase
 * Evita exposição de URLs, tokens e informações sensíveis
 */

type SupabaseErrorCode =
  | 'invalid_credentials'
  | 'user_already_exists'
  | 'email_not_confirmed'
  | 'signup_disabled'
  | 'weak_password'
  | 'invalid_email'
  | 'too_many_requests'
  | 'session_not_found'
  | 'user_not_found'
  | string;

interface SupabaseError {
  code?: SupabaseErrorCode;
  message?: string;
  status?: number;
}

class SupabaseLogger {
  private isDevelopment = process.env.NODE_ENV === 'development';

  /**
   * Mapeia códigos de erro para mensagens amigáveis ao usuário
   */
  private errorMessages: Record<string, string> = {
    'invalid_credentials': 'Email ou senha incorretos',
    'user_already_exists': 'Este email já está cadastrado',
    'email_not_confirmed': 'Verifique seu email para confirmar a conta',
    'signup_disabled': 'Cadastro temporariamente desabilitado',
    'weak_password': 'A senha deve ter pelo menos 6 caracteres',
    'invalid_email': 'Email inválido',
    'too_many_requests': 'Muitas tentativas. Tente novamente em alguns minutos',
    'session_not_found': 'Sessão expirada. Faça login novamente',
    'user_not_found': 'Usuário não encontrado',
  };

  /**
   * Log seguro de erro de autenticação
   */
  logAuthError(error: SupabaseError, context: string = 'AUTH'): string {
    // Log apenas em desenvolvimento e sem informações sensíveis
    if (this.isDevelopment) {
      console.error(`[${context}] Erro de autenticação (dev only):`, {
        code: error.code,
        status: error.status,
        // NÃO logar message que pode conter URLs
      });
    }

    // Retornar mensagem amigável para o usuário
    return this.getUserFriendlyMessage(error.code);
  }

  /**
   * Log seguro de sucesso de autenticação
   */
  logAuthSuccess(action: string, userId?: string): void {
    if (this.isDevelopment) {
      console.info(`[AUTH] ${action} bem-sucedido`, {
        userId: userId ? this.maskUserId(userId) : undefined,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Log seguro de operação do Supabase
   */
  logOperation(operation: string, success: boolean, error?: SupabaseError): void {
    if (this.isDevelopment) {
      if (success) {
        console.info(`[SUPABASE] ${operation} - Sucesso`);
      } else {
        console.error(`[SUPABASE] ${operation} - Erro (dev only):`, {
          code: error?.code,
          status: error?.status,
          // NÃO logar message que pode conter URLs
        });
      }
    }
  }

  /**
   * Retorna mensagem amigável baseada no código de erro
   */
  private getUserFriendlyMessage(errorCode?: string): string {
    if (!errorCode) {
      return 'Erro inesperado. Tente novamente.';
    }

    return this.errorMessages[errorCode] || 'Erro inesperado. Tente novamente.';
  }

  /**
   * Mascara ID do usuário para logs
   */
  private maskUserId(userId: string): string {
    if (userId.length <= 8) {
      return '***';
    }

    const start = userId.substring(0, 4);
    const end = userId.substring(userId.length - 4);
    return `${start}***${end}`;
  }

  /**
   * Verifica se uma string contém URL do Supabase
   */
  private containsSupabaseUrl(text: string): boolean {
    return text.includes('supabase.co') ||
           text.includes('supabase.io') ||
           text.includes('.supabase.') ||
           text.includes('localhost:54321');
  }

  /**
   * Sanitiza mensagem removendo URLs do Supabase
   */
  sanitizeMessage(message: string): string {
    if (this.containsSupabaseUrl(message)) {
      return 'Erro de conexão com o servidor';
    }
    return message;
  }

  /**
   * Log de erro genérico com sanitização
   */
  logError(context: string, error: any): void {
    if (this.isDevelopment) {
      // Sanitizar qualquer informação que possa conter URLs
      const sanitizedError = {
        name: error?.name,
        code: error?.code,
        status: error?.status,
        // NÃO incluir message, stack, ou outras propriedades que possam conter URLs
      };

      console.error(`[${context}] Erro (dev only):`, sanitizedError);
    }
  }

  /**
   * Wrapper seguro para operações do Supabase
   */
  async safeSupabaseOperation<T = any>(
    operation: () => Promise<{ data: T; error: any }>,
    operationName: string
  ): Promise<{ data: T | null; error: string | null }> {
    try {
      const result = await operation();

      if (result.error) {
        const userMessage = this.logAuthError(result.error, operationName);
        return { data: null, error: userMessage };
      }

      this.logOperation(operationName, true);
      return { data: result.data, error: null };
    } catch (error) {
      this.logError(operationName, error);
      return {
        data: null,
        error: 'Erro inesperado. Tente novamente.'
      };
    }
  }
}

// Singleton instance
export const supabaseLogger = new SupabaseLogger();

// Convenience exports
export default supabaseLogger;
