import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('user_id');

    if (!userId) {
      return NextResponse.json(
        { error: 'user_id é obrigatório' },
        { status: 400 }
      );
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY!
    );

    // Buscar créditos do usuário na tabela users do schema app
    const { data, error } = await supabase
      .schema('app')
      .from('users')
      .select('credits')
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Erro ao buscar créditos do usuário:', error);

      // Se o usuário não existe, criar com créditos iniciais
      if (error.code === 'PGRST116') { // No rows found
        try {
          const { error: insertError } = await supabase
            .schema('app')
            .from('users')
            .insert([{
              user_id: userId,
              credits: 10, // Créditos iniciais
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }]);

          if (insertError) {
            console.error('Erro ao criar usuário:', insertError);
            return NextResponse.json(
              { error: 'Erro ao criar usuário' },
              { status: 500 }
            );
          }

          return NextResponse.json({
            credits: 10
          });
        } catch (createError) {
          console.error('Erro ao criar usuário:', createError);
          return NextResponse.json(
            { error: 'Erro ao criar usuário' },
            { status: 500 }
          );
        }
      }

      return NextResponse.json(
        { error: 'Erro ao buscar créditos' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      credits: data?.credits || 0
    });

  } catch (error) {
    console.error('Erro na API de créditos:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { user_id, credits_to_add } = await request.json();

    if (!user_id || typeof credits_to_add !== 'number') {
      return NextResponse.json(
        { error: 'user_id e credits_to_add são obrigatórios' },
        { status: 400 }
      );
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY!
    );

    // Buscar créditos atuais
    const { data: currentData, error: fetchError } = await supabase
      .schema('app')
      .from('users')
      .select('credits')
      .eq('user_id', user_id)
      .single();

    if (fetchError) {
      console.error('Erro ao buscar créditos atuais:', fetchError);
      return NextResponse.json(
        { error: 'Erro ao buscar créditos atuais' },
        { status: 500 }
      );
    }

    const currentCredits = currentData?.credits || 0;
    const newCredits = currentCredits + credits_to_add;

    // Atualizar créditos
    const { data, error } = await supabase
      .schema('app')
      .from('users')
      .update({ credits: newCredits, updated_at: new Date().toISOString() })
      .eq('user_id', user_id)
      .select('credits')
      .single();

    if (error) {
      console.error('Erro ao atualizar créditos:', error);
      return NextResponse.json(
        { error: 'Erro ao atualizar créditos' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      credits: data.credits,
      previous_credits: currentCredits,
      added: credits_to_add
    });

  } catch (error) {
    console.error('Erro na API de créditos (POST):', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
