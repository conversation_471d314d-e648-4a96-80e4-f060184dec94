# 🔍 PROBLEMA IDENTIFICADO: CONTEXTO HTTP

## 🚨 SUSPEITA PRINCIPAL

O problema pode ser que **o contexto RLS não persiste entre chamadas HTTP diferentes**:

1. **Chamada 1**: `set_current_tenant()` → Define contexto ✅
2. **Chamada 2**: `SELECT FROM users` → Contexto perdido ❌

## 🧪 TESTES PARA CONFIRMAR

### **TESTE A: Sequência no Postman**

#### **1. Definir Contexto**
```
POST {{SUPABASE_URL}}/rest/v1/rpc/set_current_tenant
Headers: apikey, Authorization, Accept-Profile: app
Body: {"tenant_uuid": "1c11dd1a-97a8-45fd-a295-f56963f50f9a"}
```

#### **2. Imediatamente Verificar Contexto**
```
POST {{SUPABASE_URL}}/rest/v1/rpc/get_current_tenant_id
Headers: apikey, Authorization, Accept-Profile: app
Body: {}
```

#### **3. Imediatamente Buscar Usuário**
```
GET {{SUPABASE_URL}}/rest/v1/users?select=tenant_id,full_name&user_id=eq.{{USER_ID}}
Headers: apikey, Authorization, Accept-Profile: app
```

### **TESTE B: Chamada Única (Transação)**

#### **SQL em uma única chamada**
```
POST {{SUPABASE_URL}}/rest/v1/rpc/sql
Headers: apikey: SERVICE_KEY, Authorization: Bearer SERVICE_KEY
Body: {
  "query": "SELECT app.set_current_tenant('1c11dd1a-97a8-45fd-a295-f56963f50f9a'); SELECT user_id, tenant_id FROM app.users WHERE user_id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d';"
}
```

## 🔧 SOLUÇÕES POSSÍVEIS

### **Solução 1: Contexto por Sessão**
Modificar as funções para usar sessão em vez de transação:

```sql
-- Usar configuração de sessão
CREATE OR REPLACE FUNCTION app.set_current_tenant(tenant_uuid UUID)
RETURNS void AS $$
BEGIN
  PERFORM set_config('app.current_tenant_id', tenant_uuid::text, false); -- false = sessão
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### **Solução 2: Header-Based RLS**
Modificar política para usar header em vez de contexto:

```sql
-- Política baseada em header
CREATE POLICY tenant_isolation_users ON app.users
  FOR ALL USING (
    tenant_id::text = current_setting('request.headers', true)::json->>'x-tenant-id'
  );
```

### **Solução 3: Middleware Server-Side**
Garantir que o contexto seja definido em cada request no servidor.

## 📋 DIAGNÓSTICO RÁPIDO

### **Execute no SQL Editor:**
```sql
-- Teste em uma transação
BEGIN;
SELECT app.set_current_tenant('1c11dd1a-97a8-45fd-a295-f56963f50f9a');
SELECT app.get_current_tenant_id();
SELECT user_id FROM app.users WHERE user_id = '38cc3c24-7d83-4194-bcc9-68a501ceff2d';
COMMIT;
```

### **Se funciona no SQL mas não no HTTP:**
→ Problema de persistência entre requests

### **Se não funciona nem no SQL:**
→ Problema na política RLS ou função

## 🎯 PRÓXIMOS PASSOS

1. **Execute o script SQL**: `scripts/diagnostico-rls-completo.sql`
2. **Teste no Postman**: Sequência A (3 chamadas separadas)
3. **Compare resultados**: SQL vs HTTP
4. **Identifique onde falha**: Contexto, política ou função

---

**Execute os testes e me reporte os resultados! Vamos identificar se é problema de contexto HTTP. 🔍**
