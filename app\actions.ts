"use server";

import { encodedRedirect } from "@/utils/utils";
import { createClient } from "@/utils/supabase/server";
import { headers } from "next/headers";
import { redirect } from "next/navigation";

export const signUpAction = async (formData: FormData) => {
  const email = formData.get("email")?.toString();
  const password = formData.get("password")?.toString();
  const full_name = formData.get("full_name")?.toString();
  const supabase = await createClient();
  const origin = (await headers()).get("origin");
  const headersList = await headers();

  if (!email || !password) {
    return encodedRedirect(
      "error",
      "/sign-up",
      "Email e senha são obrigatórios",
    );
  }

  // Extrair subdomínio do tenant atual
  const host = headersList.get("host") || "";
  const hostname = host.split(':')[0];
  let subdomain = null;

  // Detectar subdomínio
  if (hostname.includes('localhost')) {
    const match = hostname.match(/^([^.]+)\.localhost$/);
    subdomain = match ? match[1] : null;
  } else if (hostname.includes('vascofa.shop')) {
    const match = hostname.match(/^([^.]+)\.vascofa\.shop$/);
    subdomain = match ? match[1] : null;
  }

  const { error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        full_name: full_name,
        subdomain: subdomain, // Incluir subdomínio no metadata
      },
      emailRedirectTo: `${origin}/auth/callback`,
    },
  });

  if (error) {
    // ⚠️ SEGURANÇA: Não logar detalhes do erro que podem expor URLs do Supabase
    // console.error(error.code + " " + error.message);

    // Log seguro apenas em desenvolvimento
    if (process.env.NODE_ENV === 'development') {
      console.error("Erro de cadastro (dev only):", error.code);
    }

    // Retornar mensagem genérica para o usuário
    const userMessage = error.code === 'user_already_exists'
      ? 'Este email já está cadastrado'
      : 'Erro ao criar conta. Tente novamente.';

    return encodedRedirect("error", "/sign-up", userMessage);
  } else {
    return encodedRedirect(
      "success",
      "/sign-up",
      "Obrigado por se cadastrar! Verifique seu email para o link de verificação.",
    );
  }
};

export const signInAction = async (formData: FormData) => {
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;
  const supabase = await createClient();

  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    return encodedRedirect("error", "/sign-in", error.message);
  }

  // ⚠️ REMOVIDO: console.log(data) - Expunha dados de autenticação
  // console.log(data);

  return redirect("/chat");
};

export const forgotPasswordAction = async (formData: FormData) => {
  const email = formData.get("email")?.toString();
  const supabase = await createClient();
  const origin = (await headers()).get("origin");
  const callbackUrl = formData.get("callbackUrl")?.toString();

  if (!email) {
    return encodedRedirect("error", "/forgot-password", "Email é obrigatório");
  }

  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${origin}/auth/callback?redirect_to=/chat/reset-password`,
  });

  if (error) {
    console.error(error.message);
    return encodedRedirect(
      "error",
      "/forgot-password",
      "Não foi possível redefinir a senha",
    );
  }

  if (callbackUrl) {
    return redirect(callbackUrl);
  }

  return encodedRedirect(
    "success",
    "/forgot-password",
    "Verifique seu email para o link de redefinição de senha.",
  );
};

export const resetPasswordAction = async (formData: FormData) => {
  const supabase = await createClient();

  const password = formData.get("password") as string;
  const confirmPassword = formData.get("confirmPassword") as string;

  if (!password || !confirmPassword) {
    encodedRedirect(
      "error",
      "/chat/reset-password",
      "Senha e confirmação de senha são obrigatórias",
    );
  }

  if (password !== confirmPassword) {
    encodedRedirect(
      "error",
      "/chat/reset-password",
      "As senhas não coincidem",
    );
  }

  const { error } = await supabase.auth.updateUser({
    password: password,
  });

  if (error) {
    encodedRedirect(
      "error",
      "/chat/reset-password",
      "Falha ao atualizar senha",
    );
  }

  encodedRedirect("success", "/chat/reset-password", "Senha atualizada");
};

export const signOutAction = async () => {
  const supabase = await createClient();
  await supabase.auth.signOut();
  return redirect("/sign-in");
};
