"use server";

import { encodedRedirect } from "@/utils/utils";
import { createClient } from "@/utils/supabase/server";
import { headers } from "next/headers";
import { redirect } from "next/navigation";

export const signUpAction = async (formData: FormData) => {
  const email = formData.get("email")?.toString();
  const password = formData.get("password")?.toString();
  const full_name = formData.get("full_name")?.toString();
  const supabase = await createClient();
  const origin = (await headers()).get("origin");
  const headersList = await headers();

  if (!email || !password) {
    return encodedRedirect(
      "error",
      "/sign-up",
      "Email e senha são obrigatórios",
    );
  }

  // Extrair subdomínio do tenant atual
  const host = headersList.get("host") || "";
  const hostname = host.split(':')[0];
  let subdomain = null;

  // Detectar subdomínio
  if (hostname.includes('localhost')) {
    const match = hostname.match(/^([^.]+)\.localhost$/);
    subdomain = match ? match[1] : null;
  } else if (hostname.includes('vascofa.shop')) {
    const match = hostname.match(/^([^.]+)\.vascofa\.shop$/);
    subdomain = match ? match[1] : null;
  }

  const { error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        full_name: full_name,
        subdomain: subdomain, // Incluir subdomínio no metadata
      },
      emailRedirectTo: `${origin}/auth/callback`,
    },
  });

  if (error) {
    // ⚠️ SEGURANÇA: Não logar detalhes do erro que podem expor URLs do Supabase
    // console.error(error.code + " " + error.message);

    // Log seguro apenas em desenvolvimento
    if (process.env.NODE_ENV === 'development') {
      console.error("Erro de cadastro (dev only):", error.code);
    }

    // Retornar mensagem genérica para o usuário
    const userMessage = error.code === 'user_already_exists'
      ? 'Este email já está cadastrado'
      : 'Erro ao criar conta. Tente novamente.';

    return encodedRedirect("error", "/sign-up", userMessage);
  } else {
    return encodedRedirect(
      "success",
      "/sign-up",
      "Obrigado por se cadastrar! Verifique seu email para o link de verificação.",
    );
  }
};

export const signInAction = async (formData: FormData) => {
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;
  const supabase = await createClient();
  const headersList = await headers();

  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    return encodedRedirect("error", "/sign-in", error.message);
  }

  // SEGURANÇA CRÍTICA: Validar se usuário pertence ao tenant do subdomínio
  const host = headersList.get("host") || "";
  const hostname = host.split(':')[0];
  let subdomain = null;

  // Detectar subdomínio atual
  if (hostname.includes('localhost')) {
    const match = hostname.match(/^([^.]+)\.localhost$/);
    subdomain = match ? match[1] : null;
  } else if (hostname.includes('vascofa.shop')) {
    const match = hostname.match(/^([^.]+)\.vascofa\.shop$/);
    subdomain = match ? match[1] : null;
  }

  // Se há subdomínio, validar se usuário pertence ao tenant
  if (subdomain && data.user) {
    try {
      console.log(`[DEBUG] Validando usuário ${data.user.id} para subdomínio ${subdomain}`);
      console.log(`[DEBUG] Headers disponíveis:`, Object.fromEntries(headersList.entries()));

      // Verificar se o contexto de tenant está definido
      try {
        const currentTenant = await supabase.schema('app').rpc('get_current_tenant_id');
        console.log(`[DEBUG] Tenant atual no contexto:`, currentTenant);
      } catch (contextError) {
        console.error(`[DEBUG] Erro ao verificar contexto:`, contextError);
      }

      // Primeiro, verificar se a tabela users existe e é acessível
      console.log(`[DEBUG] Testando acesso à tabela users...`);
      try {
        const { count, error: countError } = await supabase
          .schema('app')
          .from('users')
          .select('*', { count: 'exact', head: true });
        console.log(`[DEBUG] Teste de acesso à tabela users:`, { count, countError });
      } catch (tableError) {
        console.error(`[DEBUG] Erro ao acessar tabela users:`, tableError);
      }

      // Buscar tenant_id do usuário
      console.log(`[DEBUG] Buscando dados do usuário ${data.user.id}...`);
      const { data: userData, error: userError } = await supabase
        .schema('app')
        .from('users')
        .select('tenant_id, full_name, email')
        .eq('user_id', data.user.id)
        .single();

      console.log(`[DEBUG] Resultado da busca do usuário:`, { userData, userError });
      console.log(`[DEBUG] Detalhes do erro (se houver):`, userError?.details, userError?.hint, userError?.code);

      if (userError || !userData) {
        console.error(`[DEBUG] Usuário não encontrado:`, userError);
        await supabase.auth.signOut();
        return encodedRedirect("error", "/sign-in", `Usuário não encontrado no sistema. Erro: ${userError?.message || 'Desconhecido'}`);
      }

      // Buscar tenant pelo subdomínio
      const { data: tenantData, error: tenantError } = await supabase
        .schema('app')
        .from('tenants')
        .select('id, name')
        .eq('subdomain', subdomain)
        .eq('is_active', true)
        .single();

      console.log(`[DEBUG] Dados do tenant:`, { tenantData, tenantError });

      if (tenantError || !tenantData) {
        console.error(`[DEBUG] Tenant não encontrado:`, tenantError);
        await supabase.auth.signOut();
        return encodedRedirect("error", "/sign-in", `Tenant não encontrado. Erro: ${tenantError?.message || 'Desconhecido'}`);
      }

      // VALIDAÇÃO CRÍTICA: Verificar se usuário pertence ao tenant
      console.log(`[DEBUG] Comparando tenant_id: usuário=${userData.tenant_id}, tenant=${tenantData.id}`);

      if (userData.tenant_id !== tenantData.id) {
        console.error(`[DEBUG] Mismatch de tenant: usuário pertence a ${userData.tenant_id}, tentando acessar ${tenantData.id}`);
        await supabase.auth.signOut();
        return encodedRedirect("error", "/sign-in", `Acesso negado: usuário pertence a outro tenant (${userData.tenant_id} vs ${tenantData.id})`);
      }

      console.log(`[DEBUG] Validação passou! Usuário autorizado para ${tenantData.name}`);
    } catch (validationError) {
      console.error(`[DEBUG] Erro na validação:`, validationError);
      await supabase.auth.signOut();
      return encodedRedirect("error", "/sign-in", `Erro na validação de acesso: ${validationError}`);
    }
  }

  return redirect("/chat");
};

export const forgotPasswordAction = async (formData: FormData) => {
  const email = formData.get("email")?.toString();
  const supabase = await createClient();
  const origin = (await headers()).get("origin");
  const callbackUrl = formData.get("callbackUrl")?.toString();

  if (!email) {
    return encodedRedirect("error", "/forgot-password", "Email é obrigatório");
  }

  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${origin}/auth/callback?redirect_to=/chat/reset-password`,
  });

  if (error) {
    console.error(error.message);
    return encodedRedirect(
      "error",
      "/forgot-password",
      "Não foi possível redefinir a senha",
    );
  }

  if (callbackUrl) {
    return redirect(callbackUrl);
  }

  return encodedRedirect(
    "success",
    "/forgot-password",
    "Verifique seu email para o link de redefinição de senha.",
  );
};

export const resetPasswordAction = async (formData: FormData) => {
  const supabase = await createClient();

  const password = formData.get("password") as string;
  const confirmPassword = formData.get("confirmPassword") as string;

  if (!password || !confirmPassword) {
    encodedRedirect(
      "error",
      "/chat/reset-password",
      "Senha e confirmação de senha são obrigatórias",
    );
  }

  if (password !== confirmPassword) {
    encodedRedirect(
      "error",
      "/chat/reset-password",
      "As senhas não coincidem",
    );
  }

  const { error } = await supabase.auth.updateUser({
    password: password,
  });

  if (error) {
    encodedRedirect(
      "error",
      "/chat/reset-password",
      "Falha ao atualizar senha",
    );
  }

  encodedRedirect("success", "/chat/reset-password", "Senha atualizada");
};

export const signOutAction = async () => {
  const supabase = await createClient();
  await supabase.auth.signOut();
  return redirect("/sign-in");
};
