"use client";

import React, { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Menu, X, LogOut, PlusCircle } from "lucide-react";
import SessionSidebar from "./session-sidebar";
import { signOutAction } from "@/app/actions";
import { useSession } from "./session-context";

interface MobileSidebarProps {
  userId: string;
}

export default function MobileSidebar({ userId }: MobileSidebarProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { currentSessionId, createNewSession, isCreatingSession } = useSession();
  const lastSessionIdRef = useRef<string | null>(null);

  // Fechar sidebar quando uma sessão diferente for selecionada
  useEffect(() => {
    // Só fechar se realmente mudou de sessão (não na primeira renderização)
    if (lastSessionIdRef.current !== null &&
        currentSessionId &&
        currentSessionId !== lastSessionIdRef.current) {
      setIsOpen(false);
    }
    lastSessionIdRef.current = currentSessionId;
  }, [currentSessionId]); // Removido isOpen das dependências para evitar loops

  const handleSignOut = async () => {
    await signOutAction();
  };

  const handleCreateSession = async () => {
    try {
      await createNewSession(userId);
      handleCloseSidebar(); // Fechar sidebar após criar sessão
    } catch (error) {
      console.error("Erro ao criar nova sessão:", error);
    }
  };

  const handleOpenSidebar = () => {
    // Força o estado para true, independente do estado atual
    setIsOpen(true);
  };

  const handleCloseSidebar = () => {
    setIsOpen(false);
  };

  return (
    <>
      {/* Botão para abrir o sidebar */}
      <Button
        variant="ghost"
        size="icon"
        onClick={handleOpenSidebar}
        className="h-10 w-10"
      >
        <Menu className="h-5 w-5" />
        <span className="sr-only">Abrir menu</span>
      </Button>

      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40"
          onClick={handleCloseSidebar}
        />
      )}

      {/* Sidebar Drawer */}
      <div
        className={`fixed top-0 left-0 w-[85vw] max-w-sm bg-background border-r border-border z-50 transform transition-transform duration-300 ease-in-out flex flex-col ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
        style={{
          height: '100dvh', // Dynamic viewport height para navegadores modernos
          maxHeight: '100dvh'
        }}
      >
        {/* Header do drawer */}
        <div className="flex items-center justify-between p-4 border-b border-border flex-shrink-0 mobile-safe-top">
          <h2 className="text-lg font-semibold">PrimeAI</h2>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleCloseSidebar}
            className="h-8 w-8"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Fechar menu</span>
          </Button>
        </div>

        {/* Lista de sessões - área scrollável */}
        <div className="flex-1 overflow-hidden min-h-0" style={{ paddingBottom: '160px' }}>
          <SessionSidebar userId={userId} hideFooter={true} />
        </div>

        {/* Footer com nova conversa e logout - sempre visível */}
        <div
          className="absolute bottom-0 left-0 right-0 border-t border-border p-4 space-y-3 bg-background"
          style={{
            paddingBottom: 'max(2rem, calc(env(safe-area-inset-bottom, 0px) + 2rem))',
            minHeight: '140px', // Altura mínima para garantir visibilidade
            zIndex: 10
          }}
        >
          <Button
            onClick={handleCreateSession}
            disabled={isCreatingSession}
            variant="default"
            className="w-full justify-start min-h-[52px] text-sm font-medium"
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            {isCreatingSession ? "Criando..." : "Nova Conversa"}
          </Button>
          <Button
            onClick={handleSignOut}
            variant="outline"
            className="w-full justify-start min-h-[52px] text-sm"
          >
            <LogOut className="mr-2 h-4 w-4" />
            Sair
          </Button>
        </div>
      </div>
    </>
  );
}
