"use client";

import { createClient } from "@/utils/supabase/client";
import { supabaseLogger } from "@/lib/utils/supabase-logger";
import { withSupabaseInterception } from "@/lib/utils/console-interceptor";
import { AppConfig } from "@/lib/config/app-config";

// Client-side version of the actions that doesn't use next/headers
export const signOutClient = async () => {
  const supabase = createClient();
  await supabase.auth.signOut();
  // Não usamos redirect aqui, vamos usar o router no componente
  return { success: true };
};

export const signInClient = async (formData: FormData) => {
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;
  const supabase = createClient();

  // Usar interceptação automática para suprimir logs do Supabase
  return await withSupabaseInterception(async () => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({ email, password });

      if (error) {
        const userMessage = supabaseLogger.logAuthError(error, 'SIGN_IN');
        throw new Error(userMessage);
      }

      if (data?.user) {
        supabaseLogger.logAuthSuccess('Login', data.user.id);
      }

      return { success: true, user: data?.user };
    } catch (error: any) {
      // Se for um erro que já foi processado pelo logger, re-throw
      if (error.message && typeof error.message === 'string') {
        throw error;
      }

      // Caso contrário, processar como erro genérico
      supabaseLogger.logError('SIGN_IN', error);
      throw new Error('Erro inesperado. Tente novamente.');
    }
  });
};

export const signUpClient = async (formData: FormData) => {
  // Verificar se signup está habilitado
  if (!AppConfig.enableSignup) {
    throw new Error("Registro de novos usuários está desabilitado");
  }

  const email = formData.get("email")?.toString();
  const password = formData.get("password")?.toString();
  const full_name = formData.get("full_name")?.toString();
  const supabase = createClient();

  if (!email || !password) {
    throw new Error("Email e senha são obrigatórios");
  }

  return await withSupabaseInterception(async () => {
    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: full_name,
          },
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      if (error) {
        const userMessage = supabaseLogger.logAuthError(error, 'SIGN_UP');
        throw new Error(userMessage);
      }

      return { success: true, message: "Obrigado por se cadastrar! Verifique seu email para o link de verificação." };
    } catch (error: any) {
      if (error.message && typeof error.message === 'string') {
        throw error;
      }

      supabaseLogger.logError('SIGN_UP', error);
      throw new Error('Erro inesperado. Tente novamente.');
    }
  });
};

export const forgotPasswordClient = async (formData: FormData) => {
  const email = formData.get("email")?.toString();
  const supabase = createClient();

  if (!email) {
    throw new Error("Email é obrigatório");
  }

  return await withSupabaseInterception(async () => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/callback?redirect_to=/chat/reset-password`,
      });

      if (error) {
        const userMessage = supabaseLogger.logAuthError(error, 'FORGOT_PASSWORD');
        throw new Error(userMessage);
      }

      return { success: true, message: "Verifique seu email para o link de redefinição de senha." };
    } catch (error: any) {
      if (error.message && typeof error.message === 'string') {
        throw error;
      }

      supabaseLogger.logError('FORGOT_PASSWORD', error);
      throw new Error('Erro inesperado. Tente novamente.');
    }
  });
};

export const resetPasswordClient = async (formData: FormData) => {
  const supabase = createClient();

  const password = formData.get("password") as string;
  const confirmPassword = formData.get("confirmPassword") as string;

  if (!password || !confirmPassword) {
    throw new Error("Senha e confirmação de senha são obrigatórias");
  }

  if (password !== confirmPassword) {
    throw new Error("As senhas não coincidem");
  }

  return await withSupabaseInterception(async () => {
    try {
      const { error } = await supabase.auth.updateUser({
        password: password,
      });

      if (error) {
        const userMessage = supabaseLogger.logAuthError(error, 'RESET_PASSWORD');
        throw new Error(userMessage);
      }

      return { success: true, message: "Senha atualizada" };
    } catch (error: any) {
      if (error.message && typeof error.message === 'string') {
        throw error;
      }

      supabaseLogger.logError('RESET_PASSWORD', error);
      throw new Error('Erro inesperado. Tente novamente.');
    }
  });
};
