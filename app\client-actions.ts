"use client";

import { createClient } from "@/utils/supabase/client";
import { supabaseLogger } from "@/lib/utils/supabase-logger";
import { withSupabaseInterception } from "@/lib/utils/console-interceptor";
import { AppConfig } from "@/lib/config/app-config";

// Client-side version of the actions that doesn't use next/headers
export const signOutClient = async () => {
  const supabase = createClient();
  await supabase.auth.signOut();
  // Não usamos redirect aqui, vamos usar o router no componente
  return { success: true };
};

export const signInClient = async (formData: FormData) => {
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;
  const supabase = createClient();

  // Usar interceptação automática para suprimir logs do Supabase
  return await withSupabaseInterception(async () => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({ email, password });

      if (error) {
        const userMessage = supabaseLogger.logAuthError(error, 'SIGN_IN');
        throw new Error(userMessage);
      }

      // SEGURANÇA CRÍTICA: Validar se usuário pertence ao tenant do subdomínio
      const hostname = window.location.hostname;
      let subdomain = null;

      // Detectar subdomínio atual
      if (hostname.includes('localhost')) {
        const match = hostname.match(/^([^.]+)\.localhost$/);
        subdomain = match ? match[1] : null;
      } else if (hostname.includes('vascofa.shop')) {
        const match = hostname.match(/^([^.]+)\.vascofa\.shop$/);
        subdomain = match ? match[1] : null;
      }

      // Se há subdomínio, validar se usuário pertence ao tenant
      if (subdomain && data?.user) {
        try {
          console.log(`[CLIENT DEBUG] Validando usuário ${data.user.id} para subdomínio ${subdomain}`);

          // Verificar se conseguimos acessar a tabela users
          console.log(`[CLIENT DEBUG] Testando acesso à tabela users...`);
          try {
            const { count, error: countError } = await supabase
              .schema('app')
              .from('users')
              .select('*', { count: 'exact', head: true });
            console.log(`[CLIENT DEBUG] Teste de acesso à tabela users:`, { count, countError });
          } catch (tableError) {
            console.error(`[CLIENT DEBUG] Erro ao acessar tabela users:`, tableError);
          }

          // Buscar tenant_id do usuário
          console.log(`[CLIENT DEBUG] Buscando dados do usuário ${data.user.id}...`);
          const { data: userData, error: userError } = await supabase
            .schema('app')
            .from('users')
            .select('tenant_id, full_name, email')
            .eq('user_id', data.user.id)
            .single();

          console.log(`[CLIENT DEBUG] Resultado da busca do usuário:`, { userData, userError });
          console.log(`[CLIENT DEBUG] Detalhes do erro (se houver):`, userError?.details, userError?.hint, userError?.code);

          if (userError || !userData) {
            console.error(`[CLIENT DEBUG] Usuário não encontrado:`, userError);
            await supabase.auth.signOut();
            throw new Error(`Usuário não encontrado no sistema. Erro: ${userError?.message || 'Desconhecido'}`);
          }

          // Buscar tenant pelo subdomínio
          const { data: tenantData, error: tenantError } = await supabase
            .schema('app')
            .from('tenants')
            .select('id')
            .eq('subdomain', subdomain)
            .eq('is_active', true)
            .single();

          if (tenantError || !tenantData) {
            await supabase.auth.signOut();
            throw new Error('Tenant não encontrado');
          }

          // VALIDAÇÃO CRÍTICA: Verificar se usuário pertence ao tenant
          if (userData.tenant_id !== tenantData.id) {
            await supabase.auth.signOut();
            throw new Error('Acesso negado: usuário não pertence a esta empresa');
          }
        } catch (validationError: any) {
          await supabase.auth.signOut();
          throw new Error(validationError.message || 'Erro na validação de acesso');
        }
      }

      if (data?.user) {
        supabaseLogger.logAuthSuccess('Login', data.user.id);
      }

      return { success: true, user: data?.user };
    } catch (error: any) {
      // Se for um erro que já foi processado pelo logger, re-throw
      if (error.message && typeof error.message === 'string') {
        throw error;
      }

      // Caso contrário, processar como erro genérico
      supabaseLogger.logError('SIGN_IN', error);
      throw new Error('Erro inesperado. Tente novamente.');
    }
  });
};

export const signUpClient = async (formData: FormData) => {
  // Verificar se signup está habilitado
  if (!AppConfig.enableSignup) {
    throw new Error("Registro de novos usuários está desabilitado");
  }

  const email = formData.get("email")?.toString();
  const password = formData.get("password")?.toString();
  const full_name = formData.get("full_name")?.toString();
  const supabase = createClient();

  if (!email || !password) {
    throw new Error("Email e senha são obrigatórios");
  }

  // DETECTAR SUBDOMÍNIO DO TENANT (igual ao server action)
  const hostname = window.location.hostname;
  let subdomain = null;

  // Detectar subdomínio
  if (hostname.includes('localhost')) {
    const match = hostname.match(/^([^.]+)\.localhost$/);
    subdomain = match ? match[1] : null;
  } else if (hostname.includes('vascofa.shop')) {
    const match = hostname.match(/^([^.]+)\.vascofa\.shop$/);
    subdomain = match ? match[1] : null;
  }

  return await withSupabaseInterception(async () => {
    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: full_name,
            subdomain: subdomain, // ✅ INCLUIR SUBDOMÍNIO NO METADATA
          },
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      if (error) {
        const userMessage = supabaseLogger.logAuthError(error, 'SIGN_UP');
        throw new Error(userMessage);
      }

      return { success: true, message: "Obrigado por se cadastrar! Verifique seu email para o link de verificação." };
    } catch (error: any) {
      if (error.message && typeof error.message === 'string') {
        throw error;
      }

      supabaseLogger.logError('SIGN_UP', error);
      throw new Error('Erro inesperado. Tente novamente.');
    }
  });
};

export const forgotPasswordClient = async (formData: FormData) => {
  const email = formData.get("email")?.toString();
  const supabase = createClient();

  if (!email) {
    throw new Error("Email é obrigatório");
  }

  return await withSupabaseInterception(async () => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/callback?redirect_to=/chat/reset-password`,
      });

      if (error) {
        const userMessage = supabaseLogger.logAuthError(error, 'FORGOT_PASSWORD');
        throw new Error(userMessage);
      }

      return { success: true, message: "Verifique seu email para o link de redefinição de senha." };
    } catch (error: any) {
      if (error.message && typeof error.message === 'string') {
        throw error;
      }

      supabaseLogger.logError('FORGOT_PASSWORD', error);
      throw new Error('Erro inesperado. Tente novamente.');
    }
  });
};

export const resetPasswordClient = async (formData: FormData) => {
  const supabase = createClient();

  const password = formData.get("password") as string;
  const confirmPassword = formData.get("confirmPassword") as string;

  if (!password || !confirmPassword) {
    throw new Error("Senha e confirmação de senha são obrigatórias");
  }

  if (password !== confirmPassword) {
    throw new Error("As senhas não coincidem");
  }

  return await withSupabaseInterception(async () => {
    try {
      const { error } = await supabase.auth.updateUser({
        password: password,
      });

      if (error) {
        const userMessage = supabaseLogger.logAuthError(error, 'RESET_PASSWORD');
        throw new Error(userMessage);
      }

      return { success: true, message: "Senha atualizada" };
    } catch (error: any) {
      if (error.message && typeof error.message === 'string') {
        throw error;
      }

      supabaseLogger.logError('RESET_PASSWORD', error);
      throw new Error('Erro inesperado. Tente novamente.');
    }
  });
};
